import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import { config } from './config/config';
import { errorHandler } from './middleware/errorHandler';
import { authenticateToken } from './middleware/auth';
import { monitoringService, errorTracking } from './middleware/monitoring';
import { securityMiddleware, sanitizeInput, requestSizeLimit, corsOptions, securityHeaders } from './middleware/security';

// Routes
import healthRoutes from './routes/health';

// Temporarily disable other routes to get server running
// import authRoutes from './routes/auth';
// import projectRoutes from './routes/projects';
// import conversationRoutes from './routes/conversations';
// import workspaceRoutes from './routes/workspaces';
// import aiRoutes from './routes/ai';
// import enhancedAiRoutes from './routes/enhanced-ai';

// Socket.io setup
import SocketManager from './lib/socket';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: corsOptions,
  transports: ['websocket', 'polling']
});

// Trust proxy for production deployment
app.set('trust proxy', 1);

// Security middleware (applied first)
app.use(securityHeaders);
app.use(securityMiddleware);

// Request size limiting
app.use(requestSizeLimit);

// CORS configuration
app.use(cors(corsOptions));

// Logging middleware
if (process.env.NODE_ENV === 'production') {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Performance monitoring
app.use(monitoringService.performanceMiddleware());

// Health check routes (no auth required)
app.use('/', healthRoutes);

// API routes - temporarily disabled to get server running
console.log('✅ Server starting with minimal routes...');

// TODO: Re-enable these routes once export issues are fixed
// if (authRoutes) app.use('/api/auth', authRoutes);
// if (projectRoutes) app.use('/api/projects', authenticateToken, projectRoutes);
// if (conversationRoutes) app.use('/api/conversations', authenticateToken, conversationRoutes);
// if (workspaceRoutes) app.use('/api/workspaces', authenticateToken, workspaceRoutes);
// if (aiRoutes) app.use('/api/ai', authenticateToken, aiRoutes);
// if (enhancedAiRoutes) app.use('/api/enhanced-ai', authenticateToken, enhancedAiRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  });
});

// Error tracking middleware
app.use(errorTracking);

// Global error handler (must be last)
app.use(errorHandler);

// Initialize Socket.io - temporarily disabled
try {
  // const socketManager = new SocketManager(io);
  console.log('✅ Socket.io temporarily disabled for startup');
} catch (error) {
  console.error('Socket initialization error:', error);
}

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);
  
  server.close(() => {
    console.log('HTTP server closed.');
    
    // Close database connections, cleanup resources, etc.
    process.exit(0);
  });
  
  // Force close after 30 seconds
  setTimeout(() => {
    console.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const PORT = config.port || 3001;
server.listen(PORT, () => {
  console.log(`🚀 SynergyAI Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📈 Metrics: http://localhost:${PORT}/metrics`);
});

export { app, server, io };