# 🧹 Clear Mock Data Guide

This guide will help you completely clear all mock/demo data from your SynergyAI workspace and ensure it doesn't reappear on refresh.

## 🔍 Problem Analysis

The issue you're experiencing is caused by:

1. **Frontend Mock Data**: Hardcoded demo workspaces in the frontend that get recreated on page refresh
2. **Backend Seeded Data**: Demo data in the database created by the seed script
3. **Browser Storage**: Cached data in localStorage/sessionStorage
4. **Lack of API Integration**: Frontend not properly syncing with backend for delete operations

## ✅ Solution Implemented

I've created a comprehensive solution that addresses all these issues:

### 1. Database Cleanup Script
- **File**: `backend/src/scripts/clear-demo-data.ts`
- **Purpose**: Removes all demo data from the database
- **Usage**: `npm run db:clear-demo` (from backend directory)

### 2. Frontend API Integration
- **File**: `frontend/src/services/workspaceService.ts`
- **Purpose**: Proper API communication for workspace operations
- **Updated**: `frontend/src/pages/Workspaces.tsx` to use backend API

### 3. Browser Storage Cleanup
- **File**: `frontend/src/utils/clearStorage.ts`
- **Purpose**: Clear all browser storage (localStorage, sessionStorage, IndexedDB)

### 4. Automated Cleanup Script
- **File**: `clear-mock-data.js` (root directory)
- **Purpose**: One-click solution to clear both backend and frontend data

## 🚀 Quick Fix (Immediate Solution)

### Step 1: Clear Browser Storage
1. Open your browser's Developer Tools (F12)
2. Go to the **Application** tab (Chrome) or **Storage** tab (Firefox)
3. In the left sidebar, find **Local Storage** and **Session Storage**
4. Right-click and select "Clear" for both
5. Also clear **IndexedDB** if present
6. Refresh the page

### Step 2: Use the Browser Storage Cleanup Tool
1. Open the file `clear-browser-storage.html` in your browser
2. Click "Clear Storage & Reload App"
3. This will automatically clear all browser storage and reload your app

## 🔧 Complete Solution

### Option A: Using the Automated Script
```bash
# From the project root directory
npm run clear-mock-data
```

This will:
- Clear backend database (if accessible)
- Create a browser cleanup tool
- Provide step-by-step instructions

### Option B: Manual Steps

#### Backend Cleanup (if database is running)
```bash
cd backend
npm install  # if not already done
npm run db:clear-demo
```

#### Frontend Cleanup
```bash
# Open clear-browser-storage.html in your browser
# OR manually clear browser storage as described above
```

## 🛠️ Technical Changes Made

### 1. Updated Frontend Workspace Management
- Removed hardcoded mock workspaces
- Added proper API integration
- Added loading states and error handling
- Workspaces now load from backend API

### 2. Enhanced Delete Operations
- Delete operations now call backend API
- Proper error handling and user feedback
- Local state updates only after successful API calls

### 3. Storage Management
- Created utilities to clear all browser storage
- Automatic detection and cleanup of app-related data
- Support for localStorage, sessionStorage, and IndexedDB

## 🔍 Verification

After clearing the data, you should see:
1. **Empty workspace list** when you first load the app
2. **No workspaces reappearing** after page refresh
3. **Proper API calls** when creating/deleting workspaces (check Network tab)

## 🚨 Troubleshooting

### If Mock Data Still Appears:
1. **Hard refresh**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
2. **Clear browser cache**: Go to browser settings and clear browsing data
3. **Incognito/Private mode**: Test in a private browser window
4. **Check for service workers**: Disable any service workers in DevTools

### If Backend Issues:
1. **Database not running**: The cleanup script requires PostgreSQL to be running
2. **Connection issues**: Check the DATABASE_URL in backend/.env
3. **Permissions**: Ensure the database user has proper permissions

## 📝 Prevention

To prevent this issue in the future:
1. **Always use API calls** for data operations
2. **Avoid hardcoded mock data** in production code
3. **Use environment flags** to control demo data
4. **Implement proper error handling** for offline scenarios

## 🎯 Next Steps

1. **Immediate**: Use the browser storage cleanup tool
2. **Short-term**: Set up the database and run the backend cleanup
3. **Long-term**: Consider implementing user authentication to properly separate user data

---

**Need Help?** If you're still experiencing issues, check the browser console for errors and ensure the backend API is accessible.
