# Database
DATABASE_URL="postgresql://username:password@localhost:5432/thoughtsync_db"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"

# CORS
FRONTEND_URL="http://localhost:8080"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# AI APIs
OPENAI_API_KEY="your-openai-api-key"
ANTHROPIC_API_KEY="your-anthropic-api-key"
GOOGLE_API_KEY="your-google-api-key"
OPENROUTER_API_KEY="your-openrouter-api-key"

# Vector Database
PINECONE_API_KEY="your-pinecone-api-key"
PINECONE_ENVIRONMENT="us-east-1"
PINECONE_INDEX_NAME="thoughtsync"
PINECONE_BASE_URL="https://thoughtsync-5pbocj2.svc.aped-4627-b74a.pinecone.io"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100