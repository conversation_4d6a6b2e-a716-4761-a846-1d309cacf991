// Agent-specific actions for workflow.md agents

import { StateCreator } from 'zustand';
import { AppState, Goal, Milestone, ProjectSummary, PromptStrategy, ContextBundle, ExecutionTask, IterationCycle } from './types';

export interface AgentActions {
  // Project Navigator actions
  updateProjectSummary: (summary: Partial<ProjectSummary>) => void;
  addGoal: (goal: Omit<Goal, 'id' | 'createdAt'>) => void;
  updateGoal: (goalId: string, updates: Partial<Goal>) => void;
  removeGoal: (goalId: string) => void;
  addMilestone: (milestone: Omit<Milestone, 'id'>) => void;
  updateMilestone: (milestoneId: string, updates: Partial<Milestone>) => void;
  removeMilestone: (milestoneId: string) => void;
  
  // Prompt Engineering actions
  createPromptStrategy: (strategy: Omit<PromptStrategy, 'id' | 'createdAt'>) => void;
  updatePromptStrategy: (strategyId: string, updates: Partial<PromptStrategy>) => void;
  removePromptStrategy: (strategyId: string) => void;
  addContextBundle: (context: Omit<ContextBundle, 'id'>) => void;
  updateContextBundle: (contextId: string, updates: Partial<ContextBundle>) => void;
  removeContextBundle: (contextId: string) => void;
  selectContextBundle: (contextId: string, selected: boolean) => void;
  
  // Advanced Execution actions
  addExecutionTask: (task: Omit<ExecutionTask, 'id'>) => void;
  updateExecutionTask: (taskId: string, updates: Partial<ExecutionTask>) => void;
  removeExecutionTask: (taskId: string) => void;
  toggleGoalShielding: () => void;
  clearExecutionQueue: () => void;
  
  // Summarizer actions
  updateSummarizerInsights: (insights: Partial<AppState['summarizerInsights']>) => void;
  updateCondensedSummary: (summary: string) => void;
  addKeyFinding: (finding: { text: string; confidence: number }) => void;
  addContradiction: (contradiction: { text: string; severity: 'low' | 'medium' | 'high' }) => void;
  addPromisingLead: (lead: { text: string; priority: number }) => void;
  addThemeTag: (tag: { name: string; count: number }) => void;
  addVariable: (variable: { name: string; description: string; impact: string; confidence: number }) => void;
  addOpenQuestion: (question: { text: string; category: string; priority: 'low' | 'medium' | 'high' }) => void;
  
  // Iterative Loop actions
  startIterationCycle: () => void;
  endIterationCycle: (cycleId: string, insights: string[], pivotSuggestions: string[], gapsIdentified: string[]) => void;
  pauseIterationCycle: (cycleId: string) => void;
  resumeIterationCycle: (cycleId: string) => void;
  
  // Agent loading states
  setAgentLoading: (agent: keyof AppState['isLoading'], loading: boolean) => void;
}

export const createAgentActions: StateCreator<
  AppState & AgentActions,
  [],
  [],
  AgentActions
> = (set, get) => ({
  // Project Navigator actions
  updateProjectSummary: (summary) =>
    set((state) => ({
      currentProject: state.currentProject
        ? { ...state.currentProject, ...summary, lastUpdated: new Date() }
        : null,
    })),

  addGoal: (goalData) =>
    set((state) => ({
      projectGoals: [
        ...state.projectGoals,
        {
          ...goalData,
          id: `goal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
        },
      ],
    })),

  updateGoal: (goalId, updates) =>
    set((state) => ({
      projectGoals: state.projectGoals.map((goal) =>
        goal.id === goalId ? { ...goal, ...updates } : goal
      ),
    })),

  removeGoal: (goalId) =>
    set((state) => ({
      projectGoals: state.projectGoals.filter((goal) => goal.id !== goalId),
    })),

  addMilestone: (milestoneData) =>
    set((state) => ({
      projectMilestones: [
        ...state.projectMilestones,
        {
          ...milestoneData,
          id: `milestone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        },
      ],
    })),

  updateMilestone: (milestoneId, updates) =>
    set((state) => ({
      projectMilestones: state.projectMilestones.map((milestone) =>
        milestone.id === milestoneId ? { ...milestone, ...updates } : milestone
      ),
    })),

  removeMilestone: (milestoneId) =>
    set((state) => ({
      projectMilestones: state.projectMilestones.filter((milestone) => milestone.id !== milestoneId),
    })),

  // Prompt Engineering actions
  createPromptStrategy: (strategyData) =>
    set((state) => ({
      promptStrategies: [
        ...state.promptStrategies,
        {
          ...strategyData,
          id: `strategy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
        },
      ],
    })),

  updatePromptStrategy: (strategyId, updates) =>
    set((state) => ({
      promptStrategies: state.promptStrategies.map((strategy) =>
        strategy.id === strategyId ? { ...strategy, ...updates } : strategy
      ),
    })),

  removePromptStrategy: (strategyId) =>
    set((state) => ({
      promptStrategies: state.promptStrategies.filter((strategy) => strategy.id !== strategyId),
    })),

  addContextBundle: (contextData) =>
    set((state) => ({
      contextBundles: [
        ...state.contextBundles,
        {
          ...contextData,
          id: `context-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        },
      ],
    })),

  updateContextBundle: (contextId, updates) =>
    set((state) => ({
      contextBundles: state.contextBundles.map((context) =>
        context.id === contextId ? { ...context, ...updates } : context
      ),
    })),

  removeContextBundle: (contextId) =>
    set((state) => ({
      contextBundles: state.contextBundles.filter((context) => context.id !== contextId),
    })),

  selectContextBundle: (contextId, selected) =>
    set((state) => ({
      contextBundles: state.contextBundles.map((context) =>
        context.id === contextId ? { ...context, selected } : context
      ),
    })),

  // Advanced Execution actions
  addExecutionTask: (taskData) =>
    set((state) => ({
      executionQueue: [
        ...state.executionQueue,
        {
          ...taskData,
          id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        },
      ],
    })),

  updateExecutionTask: (taskId, updates) =>
    set((state) => ({
      executionQueue: state.executionQueue.map((task) =>
        task.id === taskId ? { ...task, ...updates } : task
      ),
    })),

  removeExecutionTask: (taskId) =>
    set((state) => ({
      executionQueue: state.executionQueue.filter((task) => task.id !== taskId),
    })),

  toggleGoalShielding: () =>
    set((state) => ({
      goalShieldingEnabled: !state.goalShieldingEnabled,
    })),

  clearExecutionQueue: () =>
    set(() => ({
      executionQueue: [],
    })),

  // Summarizer actions
  updateSummarizerInsights: (insights) =>
    set((state) => ({
      summarizerInsights: { ...state.summarizerInsights, ...insights },
    })),

  updateCondensedSummary: (summary) =>
    set(() => ({
      condensedSummary: summary,
    })),

  addKeyFinding: (finding) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        keyFindings: [
          ...state.summarizerInsights.keyFindings,
          {
            ...finding,
            id: `finding-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  addContradiction: (contradiction) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        contradictions: [
          ...state.summarizerInsights.contradictions,
          {
            ...contradiction,
            id: `contradiction-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  addPromisingLead: (lead) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        promisingLeads: [
          ...state.summarizerInsights.promisingLeads,
          {
            ...lead,
            id: `lead-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  addThemeTag: (tag) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        themeTags: [
          ...state.summarizerInsights.themeTags,
          {
            ...tag,
            id: `tag-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  addVariable: (variable) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        variables: [
          ...state.summarizerInsights.variables,
          {
            ...variable,
            id: `variable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  addOpenQuestion: (question) =>
    set((state) => ({
      summarizerInsights: {
        ...state.summarizerInsights,
        openQuestions: [
          ...state.summarizerInsights.openQuestions,
          {
            ...question,
            id: `question-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          },
        ],
      },
    })),

  // Iterative Loop actions
  startIterationCycle: () =>
    set((state) => {
      const cycleNumber = state.iterationHistory.length + 1;
      const newCycle: IterationCycle = {
        id: `cycle-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        cycleNumber,
        startTime: new Date(),
        insights: [],
        pivotSuggestions: [],
        gapsIdentified: [],
        status: 'active',
      };
      
      return {
        iterationHistory: [...state.iterationHistory, newCycle],
      };
    }),

  endIterationCycle: (cycleId, insights, pivotSuggestions, gapsIdentified) =>
    set((state) => ({
      iterationHistory: state.iterationHistory.map((cycle) =>
        cycle.id === cycleId
          ? {
              ...cycle,
              endTime: new Date(),
              insights,
              pivotSuggestions,
              gapsIdentified,
              status: 'completed' as const,
            }
          : cycle
      ),
    })),

  pauseIterationCycle: (cycleId) =>
    set((state) => ({
      iterationHistory: state.iterationHistory.map((cycle) =>
        cycle.id === cycleId ? { ...cycle, status: 'paused' as const } : cycle
      ),
    })),

  resumeIterationCycle: (cycleId) =>
    set((state) => ({
      iterationHistory: state.iterationHistory.map((cycle) =>
        cycle.id === cycleId ? { ...cycle, status: 'active' as const } : cycle
      ),
    })),

  // Agent loading states
  setAgentLoading: (agent, loading) =>
    set((state) => ({
      isLoading: { ...state.isLoading, [agent]: loading },
    })),
});