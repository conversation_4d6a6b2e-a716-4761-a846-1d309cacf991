import { z } from 'zod';

export const createProjectSchema = z.object({
  title: z.string().min(1, 'Project title is required').max(255),
  description: z.string().max(1000).optional(),
  scope: z.array(z.string()).default([]),
  assumptions: z.array(z.string()).default([]),
  constraints: z.array(z.string()).default([]),
  successCriteria: z.array(z.string()).default([]),
});

export const updateProjectSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  scope: z.array(z.string()).optional(),
  assumptions: z.array(z.string()).optional(),
  constraints: z.array(z.string()).optional(),
  successCriteria: z.array(z.string()).optional(),
  status: z.enum(['active', 'paused', 'completed', 'archived']).optional(),
});

export const createGoalSchema = z.object({
  text: z.string().min(1, 'Goal text is required').max(500),
  type: z.enum(['short-term', 'long-term']),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
});

export const updateGoalSchema = z.object({
  text: z.string().min(1).max(500).optional(),
  type: z.enum(['short-term', 'long-term']).optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  completed: z.boolean().optional(),
});

export const createMilestoneSchema = z.object({
  title: z.string().min(1, 'Milestone title is required').max(255),
  description: z.string().max(1000).optional(),
  targetDate: z.string().datetime().optional(),
});

export const updateMilestoneSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  targetDate: z.string().datetime().optional(),
  completed: z.boolean().optional(),
});

export const projectParamsSchema = z.object({
  id: z.string().uuid('Invalid project ID'),
});

export const goalParamsSchema = z.object({
  id: z.string().uuid('Invalid goal ID'),
});

export const milestoneParamsSchema = z.object({
  id: z.string().uuid('Invalid milestone ID'),
});

export const workspaceProjectParamsSchema = z.object({
  workspaceId: z.string().uuid('Invalid workspace ID'),
});

export type CreateProjectInput = z.infer<typeof createProjectSchema>;
export type UpdateProjectInput = z.infer<typeof updateProjectSchema>;
export type CreateGoalInput = z.infer<typeof createGoalSchema>;
export type UpdateGoalInput = z.infer<typeof updateGoalSchema>;
export type CreateMilestoneInput = z.infer<typeof createMilestoneSchema>;
export type UpdateMilestoneInput = z.infer<typeof updateMilestoneSchema>;