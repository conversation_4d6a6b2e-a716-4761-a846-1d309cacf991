
import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import useAppStore from '@/stores/useAppStore';
import { Button } from '@/components/ui/button';
import { GitBranch, Shield } from 'lucide-react';

const ConversationSidebar = () => {
  const { conversations, activeConversationId, switchConversation, createNewConversation } = useAppStore();

  return (
    <div className="flex flex-col h-full bg-gray-50 border-r">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-xl font-semibold">Conversations</h2>
        <Button onClick={createNewConversation} size="sm" variant="outline">
          New Chat
        </Button>
      </div>
      <ScrollArea className="flex-1">
        <div className="p-2">
          {conversations.length > 0 ? (
            <ul>
              {conversations.map((conv) => (
                <li
                  key={conv.id}
                  className={`p-2 rounded-md cursor-pointer ${
                    activeConversationId === conv.id ? 'bg-gray-200' : 'hover:bg-gray-100'
                  }`}
                  onClick={() => switchConversation(conv.id)}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <p className="font-semibold truncate flex-1">{conv.name}</p>
                    <div className="flex items-center gap-1">
                      {conv.parentId && (
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <GitBranch className="w-3 h-3" />
                        </div>
                      )}
                      {conv.isContextShielded && (
                        <div className="flex items-center gap-1 text-xs text-orange-600">
                          <Shield className="w-3 h-3" />
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 truncate">
                    {new Date(conv.createdAt).toLocaleDateString()}
                  </p>
                </li>
              ))}
            </ul>
          ) : (
            <p className="p-2 text-sm text-gray-500">No conversations yet.</p>
          )}
        </div>
        <div className="p-2">
           <h3 className="px-2 text-lg font-semibold">History</h3>
           <p className="px-2 text-sm text-gray-400">Core: {conversations.length > 0 && activeConversationId ? conversations.find(c => c.id === activeConversationId)?.messages.core.length || 0 : 0} messages</p>
           <p className="px-2 text-sm text-gray-400">Plan: {conversations.length > 0 && activeConversationId ? conversations.find(c => c.id === activeConversationId)?.messages.plan.length || 0 : 0} messages</p>
        </div>
      </ScrollArea>
      <div className="p-4 border-t">
        <div className="text-xs text-gray-500 space-y-1">
          <div className="flex items-center gap-2">
            <GitBranch className="w-3 h-3" />
            <span>Branched conversation</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="w-3 h-3" />
            <span>Context shielded</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationSidebar;
