
import { GoogleGenerativeAI, GenerationConfig } from '@google/generative-ai';
import OpenAI from 'openai';
import { AppState, Message, ChecklistItem } from './types';
import { ConversationActions } from './conversationActions';

export interface MessageActions {
  togglePinMessage: (panelType: 'core' | 'plan', messageId: string) => void;
  sendMessage: (panelType: 'core' | 'plan', content: string) => void;
  sendGeneralChatMessage: (content: string) => void;
  clearGeneralChat: () => void;
  refreshCompass: () => void;
  addAssistantMessage: (panelType: 'core' | 'plan', content: string) => void;
  updateAssistantMessage: (panelType: 'core' | 'plan' | 'general', messageId: string, content: string) => void;
  setLoading: (panel: 'core' | 'plan' | 'compass' | 'general', loading: boolean) => void;
  deleteMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
  editMessage: (panelType: 'core' | 'plan' | 'general', messageId: string, newContent: string) => void;
  refreshAssistantMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
  resendUserMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
}

export const createMessageActions = (
  set: (partial: Partial<AppState> | ((state: AppState) => Partial<AppState>)) => void,
  get: () => AppState,
  conversationActions: ConversationActions
): MessageActions => {
  const updateAssistantMessage = (panelType: 'core' | 'plan' | 'general', messageId: string, content: string) => {
    if (panelType === 'general') {
      set((state) => ({
        generalChatMessages: state.generalChatMessages.map(msg =>
          msg.id === messageId ? { ...msg, content } : msg
        )
      }));
    } else {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: conv.messages[panelType].map(msg =>
                    msg.id === messageId ? { ...msg, content } : msg
                  )
                }
              }
            : conv
        )
      }));
    }
  };

  const refreshCompass = async () => {
    set((state) => ({ isLoading: { ...state.isLoading, compass: true } }));
    
    // Simulate compass analysis
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const visibleMessages = conversationActions.getVisibleMessages(); // Only use non-shielded conversations
    const totalMessages = visibleMessages.length;
    
    const newSummary = `# Conversation Analysis\n\n## Current Status\n- **Total Visible Messages**: ${totalMessages}\n- **Active Conversations**: ${get().conversations.length}\n- **Shielded Conversations**: ${get().conversations.filter(c => c.isContextShielded).length}\n\n## Key Insights\nThe conversation is progressing well with both AI agents contributing to the problem-solving process across multiple conversation threads. The Core is providing detailed analysis while Plan is helping refine the approach.\n\n## Next Steps\nContinue the conversation to explore deeper solutions and validate the proposed approaches.`;
    
    const newChecklist: ChecklistItem[] = [
      { id: '1', task: 'Define core objectives', isComplete: totalMessages > 2 },
      { id: '2', task: 'Gather requirements', isComplete: totalMessages > 4 },
      { id: '3', task: 'Analyze potential solutions', isComplete: totalMessages > 6 },
      { id: '4', task: 'Validate approach', isComplete: false },
      { id: '5', task: 'Implement solution', isComplete: false },
    ];

    set({
      compassSummary: newSummary,
      compassChecklist: newChecklist,
      isLoading: { ...get().isLoading, compass: false }
    });
  };

  return {
    togglePinMessage: (panelType, messageId) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: conv.messages[panelType].map(msg =>
                    msg.id === messageId ? { ...msg, isPinned: !msg.isPinned } : msg
                  )
                }
              }
            : conv
        )
      }));
    },

    sendMessage: async (panelType, content) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      // Add user message
      const userMessage: Message = {
        id: Date.now().toString() + '_user',
        role: 'user',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], userMessage]
                }
              }
            : conv
        ),
        isLoading: { ...state.isLoading, [panelType]: true }
      }));

      // Create assistant message
      const assistantMessage: Message = {
        id: Date.now().toString() + '_assistant',
        role: 'assistant',
        content: '',
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], assistantMessage]
                }
              }
            : conv
        )
      }));

      const { modelSettings, apiKeys } = get();
      const panelSettings = modelSettings[panelType];
      const model = panelSettings.model;

      // OpenRouter API integration
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        try {
          const openai = new OpenAI({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: apiKeys.openrouter,
          });

          const isReasoningModel = model.includes('deepseek') || model.includes('r1');
          
          const completionParams: any = {
            model: model,
            messages: [
              {
                role: 'user',
                content: content,
              },
            ],
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          };

          // Add reasoning configuration for models that support it
          if (isReasoningModel) {
            completionParams.reasoning = {
              effort: 'high',
              exclude: false, // Include reasoning in response for debugging
            };
          }

          const response = await openai.chat.completions.create(completionParams);
          
          let responseContent = response.choices[0]?.message?.content || 'No response received';
          
          // Log reasoning if available using type assertion
          const messageWithReasoning = response.choices[0]?.message as any;
          if (messageWithReasoning?.reasoning) {
            console.log('OpenRouter Reasoning:', messageWithReasoning.reasoning);
          }

          updateAssistantMessage(panelType, assistantMessage.id, responseContent);

        } catch (err) {
          console.error('OpenRouter API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenRouter API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenRouter API Key. Please check your settings.';
            } else if (err.message.includes('model')) {
              errorMessage = 'Error: Model not available or invalid. Please check the model name.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // Gemini API block (with "thinking budget" logic properly placed)
      if (model.startsWith('gemini')) {
        if (!apiKeys.google) {
          updateAssistantMessage(panelType, assistantMessage.id, "Error: Google API Key not provided. Please add it in the settings.");
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          return;
        }
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        // If there is a 'thinkingBudget' setting, include it as maxTokens = thinkingBudget
        const generationConfig: GenerationConfig = {
          temperature: panelSettings.temperature,
          ...(panelSettings.hasOwnProperty('thinkingBudget') && typeof panelSettings.thinkingBudget === 'number'
            ? { maxOutputTokens: panelSettings.thinkingBudget }
            : panelSettings.maxOutputTokens
              ? { maxOutputTokens: panelSettings.maxOutputTokens }
              : {})
        };

        try {
          const geminiModel = genAI.getGenerativeModel({ model, generationConfig });
          const result = await geminiModel.generateContent(content);
          const response = await result.response;
          const text = response.text();

          updateAssistantMessage(panelType, assistantMessage.id, text);

        } catch (err) {
          console.error('Gemini API Error:', err);
          let errorMessage = 'An error occurred while communicating with the Gemini API.';
          if (err instanceof Error) {
            if (err.message.includes('API key not valid')) {
              errorMessage = 'Error: Invalid Google API Key. Please check your settings.';
            } else if (err.message.includes('permission')) {
              errorMessage = 'Error: API key does not have permission for this model. Please check your Google Cloud project.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // Different responses based on panel type (fallback simulation)
      let responses: string[];
      if (panelType === 'plan') {
        const coreContext = conversationActions.getCoreContextForPlan();
        const contextSummary = coreContext.length > 0 ? `\n\nBased on your Core conversation: ${coreContext.slice(-3).map(m => m.content.substring(0, 100)).join(', ')}...` : '';
        
        responses = [
          `I understand you're working on planning and strategy.${contextSummary}`,
          "Let me help you refine your approach and create effective prompts for Core AI:",
          "1. **Define clear objectives** - What specific outcome do you want?",
          "2. **Structure your prompt** - Break down complex requests",
          "3. **Provide context** - Give Core AI the right background",
          "\n\nWould you like me to help you draft a prompt for Core AI? I can prepare something based on our discussion."
        ];
      } else {
        responses = [
          "I understand you're working on this problem. Let me provide systematic analysis...",
          "Here are the key considerations:",
          "1. First, we need to analyze the core requirements",
          "2. Then we can identify potential solutions", 
          "3. Finally, we'll evaluate the best approach",
          "\n\nWould you like me to elaborate on any of these points?"
        ];
      }

      for (let i = 0; i < responses.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        const partialContent = responses.slice(0, i + 1).join('\n');
        
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === activeConversation.id
              ? {
                  ...conv,
                  messages: {
                    ...conv.messages,
                    [panelType]: conv.messages[panelType].map(msg =>
                      msg.id === assistantMessage.id ? { ...msg, content: partialContent } : msg
                    )
                  }
                }
              : conv
          )
        }));
      }

      set((state) => ({
        isLoading: { ...state.isLoading, [panelType]: false }
      }));

      refreshCompass();
    },

    sendGeneralChatMessage: async (content) => {
      // Add user message
      const userMessage: Message = {
        id: Date.now().toString() + '_user_general',
        role: 'user',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => ({
        generalChatMessages: [...state.generalChatMessages, userMessage],
        isLoading: { ...state.isLoading, general: true }
      }));

      // Create assistant message
      const assistantMessage: Message = {
        id: Date.now().toString() + '_assistant_general',
        role: 'assistant',
        content: '',
        isPinned: false,
        timestamp: new Date(),
      };
      
      set((state) => ({
        generalChatMessages: [...state.generalChatMessages, assistantMessage],
      }));

      const { modelSettings, apiKeys } = get();
      const panelSettings = modelSettings.general;
      const model = panelSettings.model;

      // OpenRouter API integration for general chat
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        try {
          const openai = new OpenAI({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: apiKeys.openrouter,
          });

          const isReasoningModel = model.includes('deepseek') || model.includes('r1');
          
          const completionParams: any = {
            model: model,
            messages: [
              {
                role: 'user',
                content: content,
              },
            ],
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          };

          if (isReasoningModel) {
            completionParams.reasoning = {
              effort: 'high',
              exclude: false,
            };
          }

          const response = await openai.chat.completions.create(completionParams);
          
          let responseContent = response.choices[0]?.message?.content || 'No response received';
          
          // Log reasoning if available using type assertion
          const messageWithReasoning = response.choices[0]?.message as any;
          if (messageWithReasoning?.reasoning) {
            console.log('OpenRouter General Chat Reasoning:', messageWithReasoning.reasoning);
          }

          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: responseContent } : msg
            )
          }));

        } catch (err) {
          console.error('OpenRouter General Chat API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenRouter API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenRouter API Key. Please check your settings.';
            } else {
              errorMessage = err.message;
            }
          }
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
            )
          }));
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      if (model.startsWith('gemini')) {
        if (!apiKeys.google) {
          set((state) => ({
              generalChatMessages: state.generalChatMessages.map(msg =>
                  msg.id === assistantMessage.id ? { ...msg, content: "Error: Google API Key not provided. Please add it in the settings." } : msg
              ),
              isLoading: { ...state.isLoading, general: false }
          }));
          return;
        }
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        const generationConfig: GenerationConfig = {
          temperature: panelSettings.temperature,
          ...(panelSettings.maxOutputTokens > 0 && { maxOutputTokens: panelSettings.maxOutputTokens }),
        };

        try {
            const geminiModel = genAI.getGenerativeModel({ model, generationConfig });
            const result = await geminiModel.generateContent(content);
            const response = await result.response;
            const text = response.text();

            set((state) => ({
                generalChatMessages: state.generalChatMessages.map(msg =>
                    msg.id === assistantMessage.id ? { ...msg, content: text } : msg
                )
            }));

        } catch (err) {
            console.error('Gemini API Error:', err);
            let errorMessage = 'An error occurred while communicating with the Gemini API.';
            if (err instanceof Error) {
                if (err.message.includes('API key not valid')) {
                    errorMessage = 'Error: Invalid Google API Key. Please check your settings.';
                } else if (err.message.includes('permission')) {
                    errorMessage = 'Error: API key does not have permission for this model. Please check your Google Cloud project.';
                } else {
                    errorMessage = err.message;
                }
            }
            set((state) => ({
                generalChatMessages: state.generalChatMessages.map(msg =>
                    msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
                )
            }));
        } finally {
            set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      const responses = [
        "Thinking about your general query...",
        "This is a simulated response for the general chat.",
        "I can talk about anything here!",
      ];

      for (let i = 0; i < responses.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 300));
        const partialContent = responses.slice(0, i + 1).join('\n');
        
        set((state) => ({
          generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: partialContent } : msg
          )
        }));
      }

      set((state) => ({
        isLoading: { ...state.isLoading, general: false }
      }));
    },

    clearGeneralChat: () => {
      set({ generalChatMessages: [] });
    },

    refreshCompass,

    addAssistantMessage: (panelType, content) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      const message: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], message]
                }
              }
            : conv
        )
      }));
    },

    updateAssistantMessage,

    setLoading: (panel, loading) => {
      set((state) => ({
        isLoading: { ...state.isLoading, [panel]: loading }
      }));
    },

    deleteMessage: (panelType, messageId) => {
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.filter(msg => msg.id !== messageId)
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (!activeConversation) return;

        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === activeConversation.id
              ? {
                  ...conv,
                  messages: {
                    ...conv.messages,
                    [panelType]: conv.messages[panelType].filter(msg => msg.id !== messageId)
                  }
                }
              : conv
          )
        }));
      }
    },

    editMessage: (panelType, messageId, newContent) => {
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.map(msg =>
            msg.id === messageId ? { ...msg, content: newContent } : msg
          )
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (!activeConversation) return;

        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === activeConversation.id
              ? {
                  ...conv,
                  messages: {
                    ...conv.messages,
                    [panelType]: conv.messages[panelType].map(msg =>
                      msg.id === messageId ? { ...msg, content: newContent } : msg
                    )
                  }
                }
              : conv
          )
        }));
      }
    },

    refreshAssistantMessage: async (panelType, messageId) => {
      // Find the message and regenerate response
      let targetMessage: Message | undefined;
      
      if (panelType === 'general') {
        targetMessage = get().generalChatMessages.find(msg => msg.id === messageId);
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          targetMessage = activeConversation.messages[panelType].find(msg => msg.id === messageId);
        }
      }

      if (!targetMessage || targetMessage.role !== 'assistant') return;

      // Set loading state
      set((state) => ({
        isLoading: { ...state.isLoading, [panelType]: true }
      }));

      // Simulate regenerating response
      const responses = [
        "Let me provide you with a fresh perspective on this...",
        "Here's an updated analysis of your request:",
        "After reconsidering, I think we should approach this differently:",
        "\n\nThis refreshed response aims to provide better insights."
      ];

      // Clear the message content first
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.map(msg =>
            msg.id === messageId ? { ...msg, content: '' } : msg
          )
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          set((state) => ({
            conversations: state.conversations.map(conv =>
              conv.id === activeConversation.id
                ? {
                    ...conv,
                    messages: {
                      ...conv.messages,
                      [panelType]: conv.messages[panelType].map(msg =>
                        msg.id === messageId ? { ...msg, content: '' } : msg
                      )
                    }
                  }
                : conv
            )
          }));
        }
      }

      // Stream the new response
      for (let i = 0; i < responses.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 400));
        const partialContent = responses.slice(0, i + 1).join('\n');
        
        if (panelType === 'general') {
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === messageId ? { ...msg, content: partialContent } : msg
            )
          }));
        } else {
          const activeConversation = conversationActions.getActiveConversation();
          if (activeConversation) {
            set((state) => ({
              conversations: state.conversations.map(conv =>
                conv.id === activeConversation.id
                  ? {
                      ...conv,
                      messages: {
                        ...conv.messages,
                        [panelType]: conv.messages[panelType].map(msg =>
                          msg.id === messageId ? { ...msg, content: partialContent } : msg
                        )
                      }
                    }
                  : conv
              )
            }));
          }
        }
      }

      set((state) => ({
        isLoading: { ...state.isLoading, [panelType]: false }
      }));
    },

    resendUserMessage: async (panelType, messageId) => {
      // Find the user message and resend it
      let targetMessage: Message | undefined;
      
      if (panelType === 'general') {
        targetMessage = get().generalChatMessages.find(msg => msg.id === messageId);
        if (targetMessage && targetMessage.role === 'user') {
          // Create a reference to the sendGeneralChatMessage function
          const actions = get() as any;
          if (actions.sendGeneralChatMessage) {
            actions.sendGeneralChatMessage(targetMessage.content);
          }
        }
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          targetMessage = activeConversation.messages[panelType].find(msg => msg.id === messageId);
          if (targetMessage && targetMessage.role === 'user') {
            // Create a reference to the sendMessage function  
            const actions = get() as any;
            if (actions.sendMessage) {
              actions.sendMessage(panelType, targetMessage.content);
            }
          }
        }
      }
    },
  };
};
