import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { prisma } from './prisma';

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    username: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface SocketData {
  userId: string;
  workspaceId?: string;
  projectId?: string;
  conversationId?: string;
}

class SocketManager {
  private io: SocketIOServer;
  private userSockets: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private workspaceRooms: Map<string, Set<string>> = new Map(); // workspaceId -> Set of userIds
  private projectRooms: Map<string, Set<string>> = new Map(); // projectId -> Set of userIds
  private conversationRooms: Map<string, Set<string>> = new Map(); // conversationId -> Set of userIds

  constructor(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: config.frontendUrl,
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwtSecret) as { userId: string };
        
        // Verify user exists and is active
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: { id: true, username: true, firstName: true, lastName: true, isActive: true }
        });

        if (!user || !user.isActive) {
          return next(new Error('Invalid or expired token'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`User ${socket.user?.username} connected (${socket.id})`);
      
      // Track user connection
      this.trackUserConnection(socket);

      // Setup event handlers
      this.setupUserEvents(socket);
      this.setupWorkspaceEvents(socket);
      this.setupProjectEvents(socket);
      this.setupConversationEvents(socket);
      this.setupAIEvents(socket);

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });
    });
  }

  private trackUserConnection(socket: AuthenticatedSocket) {
    const userId = socket.userId!;
    
    // Add socket to user's socket set
    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, new Set());
    }
    this.userSockets.get(userId)!.add(socket.id);
    
    // Map socket to user
    this.socketUsers.set(socket.id, userId);

    // Broadcast user online status
    this.broadcastUserStatus(userId, 'online');
  }

  private setupUserEvents(socket: AuthenticatedSocket) {
    // User presence events
    socket.on('user:set-status', (status: 'online' | 'away' | 'busy') => {
      this.broadcastUserStatus(socket.userId!, status);
    });

    // Typing indicators
    socket.on('typing:start', (data: { conversationId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('typing:start', {
        userId: socket.userId,
        user: socket.user,
        conversationId: data.conversationId
      });
    });

    socket.on('typing:stop', (data: { conversationId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('typing:stop', {
        userId: socket.userId,
        conversationId: data.conversationId
      });
    });
  }

  private setupWorkspaceEvents(socket: AuthenticatedSocket) {
    // Join workspace room
    socket.on('workspace:join', async (workspaceId: string) => {
      try {
        // Verify user has access to workspace
        const access = await this.verifyWorkspaceAccess(socket.userId!, workspaceId);
        if (!access) {
          socket.emit('error', { message: 'Access denied to workspace' });
          return;
        }

        // Join workspace room
        socket.join(`workspace:${workspaceId}`);
        
        // Track user in workspace
        if (!this.workspaceRooms.has(workspaceId)) {
          this.workspaceRooms.set(workspaceId, new Set());
        }
        this.workspaceRooms.get(workspaceId)!.add(socket.userId!);

        // Notify others in workspace
        socket.to(`workspace:${workspaceId}`).emit('workspace:user-joined', {
          user: socket.user,
          workspaceId
        });

        // Send current workspace members
        const members = Array.from(this.workspaceRooms.get(workspaceId) || []);
        socket.emit('workspace:members', { workspaceId, members });

      } catch (error) {
        console.error('Error joining workspace:', error);
        socket.emit('error', { message: 'Failed to join workspace' });
      }
    });

    // Leave workspace room
    socket.on('workspace:leave', (workspaceId: string) => {
      socket.leave(`workspace:${workspaceId}`);
      
      // Remove user from workspace tracking
      this.workspaceRooms.get(workspaceId)?.delete(socket.userId!);
      
      // Notify others
      socket.to(`workspace:${workspaceId}`).emit('workspace:user-left', {
        user: socket.user,
        workspaceId
      });
    });
  }

  private setupProjectEvents(socket: AuthenticatedSocket) {
    // Join project room
    socket.on('project:join', async (projectId: string) => {
      try {
        const access = await this.verifyProjectAccess(socket.userId!, projectId);
        if (!access) {
          socket.emit('error', { message: 'Access denied to project' });
          return;
        }

        socket.join(`project:${projectId}`);
        
        if (!this.projectRooms.has(projectId)) {
          this.projectRooms.set(projectId, new Set());
        }
        this.projectRooms.get(projectId)!.add(socket.userId!);

        socket.to(`project:${projectId}`).emit('project:user-joined', {
          user: socket.user,
          projectId
        });

      } catch (error) {
        console.error('Error joining project:', error);
        socket.emit('error', { message: 'Failed to join project' });
      }
    });

    // Leave project room
    socket.on('project:leave', (projectId: string) => {
      socket.leave(`project:${projectId}`);
      this.projectRooms.get(projectId)?.delete(socket.userId!);
      
      socket.to(`project:${projectId}`).emit('project:user-left', {
        user: socket.user,
        projectId
      });
    });

    // Project updates
    socket.on('project:goal-updated', (data: { projectId: string; goal: any }) => {
      socket.to(`project:${data.projectId}`).emit('project:goal-updated', {
        goal: data.goal,
        updatedBy: socket.user,
        timestamp: new Date()
      });
    });

    socket.on('project:milestone-updated', (data: { projectId: string; milestone: any }) => {
      socket.to(`project:${data.projectId}`).emit('project:milestone-updated', {
        milestone: data.milestone,
        updatedBy: socket.user,
        timestamp: new Date()
      });
    });
  }

  private setupConversationEvents(socket: AuthenticatedSocket) {
    // Join conversation room
    socket.on('conversation:join', async (conversationId: string) => {
      try {
        const access = await this.verifyConversationAccess(socket.userId!, conversationId);
        if (!access) {
          socket.emit('error', { message: 'Access denied to conversation' });
          return;
        }

        socket.join(`conversation:${conversationId}`);
        
        if (!this.conversationRooms.has(conversationId)) {
          this.conversationRooms.set(conversationId, new Set());
        }
        this.conversationRooms.get(conversationId)!.add(socket.userId!);

        socket.to(`conversation:${conversationId}`).emit('conversation:user-joined', {
          user: socket.user,
          conversationId
        });

      } catch (error) {
        console.error('Error joining conversation:', error);
        socket.emit('error', { message: 'Failed to join conversation' });
      }
    });

    // Leave conversation room
    socket.on('conversation:leave', (conversationId: string) => {
      socket.leave(`conversation:${conversationId}`);
      this.conversationRooms.get(conversationId)?.delete(socket.userId!);
      
      socket.to(`conversation:${conversationId}`).emit('conversation:user-left', {
        user: socket.user,
        conversationId
      });
    });

    // Message events
    socket.on('message:sent', (data: { conversationId: string; message: any }) => {
      socket.to(`conversation:${data.conversationId}`).emit('message:sent', {
        message: data.message,
        sentBy: socket.user,
        timestamp: new Date()
      });
    });

    socket.on('message:updated', (data: { conversationId: string; message: any }) => {
      socket.to(`conversation:${data.conversationId}`).emit('message:updated', {
        message: data.message,
        updatedBy: socket.user,
        timestamp: new Date()
      });
    });

    socket.on('message:deleted', (data: { conversationId: string; messageId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('message:deleted', {
        messageId: data.messageId,
        deletedBy: socket.user,
        timestamp: new Date()
      });
    });
  }

  private setupAIEvents(socket: AuthenticatedSocket) {
    // AI processing events
    socket.on('ai:task-started', (data: { taskId: string; conversationId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('ai:task-started', {
        taskId: data.taskId,
        startedBy: socket.user,
        timestamp: new Date()
      });
    });

    socket.on('ai:task-progress', (data: { taskId: string; conversationId: string; progress: number }) => {
      socket.to(`conversation:${data.conversationId}`).emit('ai:task-progress', {
        taskId: data.taskId,
        progress: data.progress,
        timestamp: new Date()
      });
    });

    socket.on('ai:task-completed', (data: { taskId: string; conversationId: string; result: any }) => {
      socket.to(`conversation:${data.conversationId}`).emit('ai:task-completed', {
        taskId: data.taskId,
        result: data.result,
        timestamp: new Date()
      });
    });

    socket.on('ai:insights-updated', (data: { conversationId: string; insights: any }) => {
      socket.to(`conversation:${data.conversationId}`).emit('ai:insights-updated', {
        insights: data.insights,
        updatedBy: socket.user,
        timestamp: new Date()
      });
    });
  }

  private handleDisconnection(socket: AuthenticatedSocket) {
    const userId = socket.userId!;
    console.log(`User ${socket.user?.username} disconnected (${socket.id})`);

    // Remove socket from tracking
    this.userSockets.get(userId)?.delete(socket.id);
    this.socketUsers.delete(socket.id);

    // If user has no more sockets, mark as offline
    if (this.userSockets.get(userId)?.size === 0) {
      this.userSockets.delete(userId);
      this.broadcastUserStatus(userId, 'offline');
    }

    // Remove from room tracking
    this.workspaceRooms.forEach((users, workspaceId) => {
      if (users.has(userId) && this.userSockets.get(userId)?.size === 0) {
        users.delete(userId);
        this.io.to(`workspace:${workspaceId}`).emit('workspace:user-left', {
          user: socket.user,
          workspaceId
        });
      }
    });

    this.projectRooms.forEach((users, projectId) => {
      if (users.has(userId) && this.userSockets.get(userId)?.size === 0) {
        users.delete(userId);
        this.io.to(`project:${projectId}`).emit('project:user-left', {
          user: socket.user,
          projectId
        });
      }
    });

    this.conversationRooms.forEach((users, conversationId) => {
      if (users.has(userId) && this.userSockets.get(userId)?.size === 0) {
        users.delete(userId);
        this.io.to(`conversation:${conversationId}`).emit('conversation:user-left', {
          user: socket.user,
          conversationId
        });
      }
    });
  }

  private broadcastUserStatus(userId: string, status: 'online' | 'offline' | 'away' | 'busy') {
    // Broadcast to all workspaces where user is a member
    this.workspaceRooms.forEach((users, workspaceId) => {
      if (users.has(userId)) {
        this.io.to(`workspace:${workspaceId}`).emit('user:status-changed', {
          userId,
          status,
          timestamp: new Date()
        });
      }
    });
  }

  private async verifyWorkspaceAccess(userId: string, workspaceId: string): Promise<boolean> {
    try {
      const workspace = await prisma.workspace.findUnique({
        where: { id: workspaceId },
        include: {
          members: {
            where: { userId },
            select: { role: true }
          },
          owner: {
            select: { id: true }
          }
        }
      });

      if (!workspace) return false;

      const isOwner = workspace.owner.id === userId;
      const isMember = workspace.members.length > 0;

      return isOwner || isMember;
    } catch (error) {
      console.error('Error verifying workspace access:', error);
      return false;
    }
  }

  private async verifyProjectAccess(userId: string, projectId: string): Promise<boolean> {
    try {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          workspace: {
            include: {
              members: {
                where: { userId },
                select: { role: true }
              },
              owner: {
                select: { id: true }
              }
            }
          }
        }
      });

      if (!project) return false;

      const isOwner = project.workspace.owner.id === userId;
      const isMember = project.workspace.members.length > 0;

      return isOwner || isMember;
    } catch (error) {
      console.error('Error verifying project access:', error);
      return false;
    }
  }

  private async verifyConversationAccess(userId: string, conversationId: string): Promise<boolean> {
    try {
      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        include: {
          project: {
            include: {
              workspace: {
                include: {
                  members: {
                    where: { userId },
                    select: { role: true }
                  },
                  owner: {
                    select: { id: true }
                  }
                }
              }
            }
          }
        }
      });

      if (!conversation) return false;

      const isOwner = conversation.project.workspace.owner.id === userId;
      const isMember = conversation.project.workspace.members.length > 0;

      return isOwner || isMember;
    } catch (error) {
      console.error('Error verifying conversation access:', error);
      return false;
    }
  }

  // Public methods for emitting events from API routes
  public emitToWorkspace(workspaceId: string, event: string, data: any) {
    this.io.to(`workspace:${workspaceId}`).emit(event, data);
  }

  public emitToProject(projectId: string, event: string, data: any) {
    this.io.to(`project:${projectId}`).emit(event, data);
  }

  public emitToConversation(conversationId: string, event: string, data: any) {
    this.io.to(`conversation:${conversationId}`).emit(event, data);
  }

  public emitToUser(userId: string, event: string, data: any) {
    const userSockets = this.userSockets.get(userId);
    if (userSockets) {
      userSockets.forEach(socketId => {
        this.io.to(socketId).emit(event, data);
      });
    }
  }

  public getOnlineUsers(): string[] {
    return Array.from(this.userSockets.keys());
  }

  public getUsersInWorkspace(workspaceId: string): string[] {
    return Array.from(this.workspaceRooms.get(workspaceId) || []);
  }

  public getUsersInProject(projectId: string): string[] {
    return Array.from(this.projectRooms.get(projectId) || []);
  }

  public getUsersInConversation(conversationId: string): string[] {
    return Array.from(this.conversationRooms.get(conversationId) || []);
  }
}

export default SocketManager;