// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users & Authentication
model User {
  id           String   @id @default(uuid())
  email        String   @unique
  username     String   @unique
  passwordHash String   @map("password_hash")
  firstName    String?  @map("first_name")
  lastName     String?  @map("last_name")
  avatarUrl    String?  @map("avatar_url")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  lastLogin    DateTime? @map("last_login")
  isActive     Boolean  @default(true) @map("is_active")

  // Relations
  ownedWorkspaces    Workspace[]       @relation("WorkspaceOwner")
  workspaceMembers   WorkspaceMember[]
  createdProjects    Project[]         @relation("ProjectCreator")
  createdConversations Conversation[] @relation("ConversationCreator")
  messages           Message[]
  createdGoals       Goal[]            @relation("GoalCreator")
  createdMilestones  Milestone[]       @relation("MilestoneCreator")
  createdStrategies  PromptStrategy[]  @relation("StrategyCreator")
  createdBundles     ContextBundle[]   @relation("BundleCreator")
  executionTasks     ExecutionTask[]   @relation("TaskCreator")
  iterationCycles    IterationCycle[]  @relation("CycleCreator")
  sessions           UserSession[]

  @@map("users")
}

model UserSession {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  tokenHash String   @map("token_hash")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// Workspaces & Projects
model Workspace {
  id          String   @id @default(uuid())
  name        String
  description String?
  ownerId     String   @map("owner_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  settings    Json     @default("{}")

  // Relations
  owner            User                @relation("WorkspaceOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  members          WorkspaceMember[]
  projects         Project[]
  promptStrategies PromptStrategy[]
  contextBundles   ContextBundle[]

  @@map("workspaces")
}

model WorkspaceMember {
  workspaceId String   @map("workspace_id")
  userId      String   @map("user_id")
  role        String   @default("member") // owner, admin, member, viewer
  joinedAt    DateTime @default(now()) @map("joined_at")

  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([workspaceId, userId])
  @@map("workspace_members")
}

model Project {
  id              String   @id @default(uuid())
  workspaceId     String   @map("workspace_id")
  title           String
  description     String?
  scope           String[] @default([])
  assumptions     String[] @default([])
  constraints     String[] @default([])
  successCriteria String[] @default([]) @map("success_criteria")
  status          String   @default("active") // active, paused, completed, archived
  createdBy       String   @map("created_by")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  workspace        Workspace        @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  creator          User             @relation("ProjectCreator", fields: [createdBy], references: [id])
  conversations    Conversation[]
  goals            Goal[]
  milestones       Milestone[]
  iterationCycles  IterationCycle[]

  @@map("projects")
}

// Conversations & Messages
model Conversation {
  id                String   @id @default(uuid())
  projectId         String   @map("project_id")
  name              String
  parentId          String?  @map("parent_id")
  isContextShielded Boolean  @default(false) @map("is_context_shielded")
  createdBy         String   @map("created_by")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  project       Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator       User           @relation("ConversationCreator", fields: [createdBy], references: [id])
  parent        Conversation?  @relation("ConversationBranch", fields: [parentId], references: [id])
  branches      Conversation[] @relation("ConversationBranch")
  messages      Message[]
  executionTasks ExecutionTask[]
  insights      Insight[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(uuid())
  conversationId String   @map("conversation_id")
  agentType      String   @map("agent_type") // project-navigator, prompt-engineering, ai-execution, summarizer, general
  role           String   // user, assistant
  content        String
  metadata       Json     @default("{}")
  isPinned       Boolean  @default(false) @map("is_pinned")
  createdBy      String   @map("created_by")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  creator      User         @relation(fields: [createdBy], references: [id])

  @@map("messages")
}

// Goals & Milestones
model Goal {
  id          String    @id @default(uuid())
  projectId   String    @map("project_id")
  text        String
  type        String    // short-term, long-term
  priority    String    @default("medium") // low, medium, high
  completed   Boolean   @default(false)
  completedAt DateTime? @map("completed_at")
  createdBy   String    @map("created_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator User    @relation("GoalCreator", fields: [createdBy], references: [id])

  @@map("goals")
}

model Milestone {
  id          String    @id @default(uuid())
  projectId   String    @map("project_id")
  title       String
  description String?
  targetDate  DateTime? @map("target_date")
  completed   Boolean   @default(false)
  completedAt DateTime? @map("completed_at")
  createdBy   String    @map("created_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator User    @relation("MilestoneCreator", fields: [createdBy], references: [id])

  @@map("milestones")
}

// AI & Context Management
model PromptStrategy {
  id                  String   @id @default(uuid())
  workspaceId         String   @map("workspace_id")
  name                String
  queryStructure      String   @map("query_structure")
  contextDependencies String[] @default([]) @map("context_dependencies")
  targetModel         String   @map("target_model")
  template            String
  isPublic            Boolean  @default(false) @map("is_public")
  createdBy           String   @map("created_by")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  creator        User            @relation("StrategyCreator", fields: [createdBy], references: [id])
  executionTasks ExecutionTask[]

  @@map("prompt_strategies")
}

model ContextBundle {
  id          String   @id @default(uuid())
  workspaceId String   @map("workspace_id")
  name        String
  content     String
  type        String   // summary, output, external, user-input
  weight      Decimal  @default(1.0) @db.Decimal(3, 2)
  metadata    Json     @default("{}")
  createdBy   String   @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  creator   User      @relation("BundleCreator", fields: [createdBy], references: [id])

  @@map("context_bundles")
}

model ExecutionTask {
  id                String    @id @default(uuid())
  conversationId    String    @map("conversation_id")
  promptStrategyId  String?   @map("prompt_strategy_id")
  model             String
  status            String    @default("queued") // queued, processing, completed, failed
  progress          Int       @default(0)
  cost              Decimal   @default(0) @db.Decimal(10, 6)
  startTime         DateTime? @map("start_time")
  endTime           DateTime? @map("end_time")
  result            Json?
  errorMessage      String?   @map("error_message")
  createdBy         String    @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // Relations
  conversation    Conversation    @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  promptStrategy  PromptStrategy? @relation(fields: [promptStrategyId], references: [id])
  creator         User            @relation("TaskCreator", fields: [createdBy], references: [id])

  @@map("execution_tasks")
}

// Insights & Analytics
model Insight {
  id             String   @id @default(uuid())
  conversationId String   @map("conversation_id")
  type           String   // key-finding, contradiction, promising-lead, theme-tag, variable, open-question
  content        String
  confidence     Int?     // 0-100 for findings
  severity       String?  // for contradictions: low, medium, high
  priority       Int?     // for leads and questions
  category       String?  // for questions and themes
  metadata       Json     @default("{}")
  createdAt      DateTime @default(now()) @map("created_at")

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("insights")
}

model IterationCycle {
  id               String    @id @default(uuid())
  projectId        String    @map("project_id")
  cycleNumber      Int       @map("cycle_number")
  status           String    @default("active") // active, completed, paused
  insights         String[]  @default([])
  pivotSuggestions String[]  @default([]) @map("pivot_suggestions")
  gapsIdentified   String[]  @default([]) @map("gaps_identified")
  startTime        DateTime  @default(now()) @map("start_time")
  endTime          DateTime? @map("end_time")
  createdBy        String    @map("created_by")

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator User    @relation("CycleCreator", fields: [createdBy], references: [id])

  @@map("iteration_cycles")
}