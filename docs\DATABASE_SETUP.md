# 🗄️ Database Setup Guide for SynergyAI

This guide will help you set up PostgreSQL database for SynergyAI.

## 🎯 Database URL Format

The `DATABASE_URL` follows this format:
```
postgresql://username:password@host:port/database_name
```

## 🐳 Option 1: Docker Setup (Recommended)

**✅ Easiest option - Everything is automated!**

### Step 1: Use Docker Compose
When you run `docker-compose up -d`, PostgreSQL is automatically set up with:
- **Username**: `thoughtsync`
- **Password**: `thoughtsync_password` (or your custom password)
- **Database**: `thoughtsync_db`
- **Host**: `localhost`
- **Port**: `5432`

### Step 2: Set Your DATABASE_URL
```bash
# In your .env file
DATABASE_URL="postgresql://thoughtsync:thoughtsync_password@localhost:5432/thoughtsync_db"
```

### Step 3: Custom Password (Optional)
To use a custom password, set it in your `.env`:
```bash
# Custom database password
POSTGRES_PASSWORD=your_secure_password

# Update DATABASE_URL accordingly
DATABASE_URL="postgresql://thoughtsync:your_secure_password@localhost:5432/thoughtsync_db"
```

---

## 💻 Option 2: Local PostgreSQL Installation

**If you prefer to install PostgreSQL locally:**

### Step 1: Install PostgreSQL

#### **Windows:**
1. Download from [PostgreSQL.org](https://www.postgresql.org/download/windows/)
2. Run the installer
3. Remember the password you set for the `postgres` user
4. Default port is `5432`

#### **macOS:**
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create a user
createuser -s your_username
```

#### **Ubuntu/Linux:**
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start the service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create user and database
sudo -u postgres createuser --interactive your_username
sudo -u postgres createdb thoughtsync_db
```

### Step 2: Create Database and User
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create user
CREATE USER thoughtsync WITH PASSWORD 'your_password';

-- Create database
CREATE DATABASE thoughtsync_db OWNER thoughtsync;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE thoughtsync_db TO thoughtsync;

-- Exit
\q
```

### Step 3: Set Your DATABASE_URL
```bash
# In your .env file
DATABASE_URL="postgresql://thoughtsync:your_password@localhost:5432/thoughtsync_db"
```

---

## ☁️ Option 3: Cloud Database

**For production or if you prefer cloud hosting:**

### Popular Cloud Providers:

#### **Supabase (Free Tier Available)**
1. Go to [supabase.com](https://supabase.com/)
2. Create a new project
3. Go to Settings → Database
4. Copy the connection string
```bash
DATABASE_URL="********************************************************************/postgres"
```

#### **Railway (Free Tier Available)**
1. Go to [railway.app](https://railway.app/)
2. Create a new project
3. Add PostgreSQL service
4. Copy the DATABASE_URL from the variables tab

#### **Neon (Free Tier Available)**
1. Go to [neon.tech](https://neon.tech/)
2. Create a new project
3. Copy the connection string from the dashboard

#### **AWS RDS**
1. Create RDS PostgreSQL instance
2. Configure security groups
3. Use the endpoint in your DATABASE_URL

---

## 🔧 Configuration Examples

### Development (.env)
```bash
# Docker setup (recommended)
DATABASE_URL="postgresql://thoughtsync:thoughtsync_password@localhost:5432/thoughtsync_db"

# Local PostgreSQL
DATABASE_URL="postgresql://your_username:your_password@localhost:5432/thoughtsync_db"
```

### Production (.env.production)
```bash
# Cloud database
DATABASE_URL="******************************************************/thoughtsync_prod"

# Or with SSL (recommended for production)
DATABASE_URL="******************************************************/thoughtsync_prod?sslmode=require"
```

---

## ✅ Verify Your Database Setup

### 1. Test Connection
```bash
# Test with psql
psql "postgresql://thoughtsync:your_password@localhost:5432/thoughtsync_db"

# Should connect without errors
```

### 2. Run Migrations
```bash
# In your project directory
cd backend
npm run db:migrate

# Should create all tables successfully
```

### 3. Check Health Endpoint
```bash
# Start your app
npm run dev

# Check database health
curl http://localhost:3001/health/detailed

# Should show database: "healthy"
```

---

## 🚨 Troubleshooting

### "Connection refused" Error
```bash
# Check if PostgreSQL is running
# Docker:
docker-compose ps

# Local installation:
# Windows: Check Services
# macOS: brew services list | grep postgresql
# Linux: sudo systemctl status postgresql
```

### "Authentication failed" Error
- Double-check username and password
- Ensure user has proper permissions
- Check if database exists

### "Database does not exist" Error
```sql
-- Create the database
CREATE DATABASE thoughtsync_db;
```

### "Permission denied" Error
```sql
-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE thoughtsync_db TO thoughtsync;
```

### Port Already in Use
```bash
# Check what's using port 5432
# Windows: netstat -an | findstr 5432
# macOS/Linux: lsof -i :5432

# Change port in docker-compose.yml if needed
ports:
  - "5433:5432"  # Use port 5433 instead
```

---

## 💡 Best Practices

### Security
1. **Use strong passwords** (minimum 12 characters)
2. **Different credentials** for development and production
3. **Enable SSL** for production databases
4. **Regular backups** for production data

### Performance
1. **Connection pooling** (handled by Prisma)
2. **Regular maintenance** (VACUUM, ANALYZE)
3. **Monitor query performance**
4. **Proper indexing** (handled by migrations)

### Development
1. **Use Docker** for consistent environment
2. **Separate databases** for different environments
3. **Regular migration runs** to keep schema updated
4. **Database seeding** for test data

---

## 📞 Need Help?

### Quick Checks
```bash
# 1. Check if database is accessible
psql $DATABASE_URL -c "SELECT version();"

# 2. Check if tables exist
psql $DATABASE_URL -c "\dt"

# 3. Test app connection
curl http://localhost:3001/health/detailed
```

### Common Solutions
- **Docker not running**: Start Docker Desktop
- **Port conflicts**: Change port in docker-compose.yml
- **Permission issues**: Check user privileges
- **Connection timeout**: Check firewall settings

Choose the option that works best for your setup! Docker is recommended for development as it's the easiest to set up and matches the production environment.