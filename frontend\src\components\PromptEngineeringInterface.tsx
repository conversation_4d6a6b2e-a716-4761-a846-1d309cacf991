import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Brain, Database, Play, Save, Send, Plus, X, Weight, Eye, Settings } from 'lucide-react';
import useAppStore from '../stores/useAppStore';

const PromptEngineeringInterface: React.FC = () => {
  const { 
    promptStrategies, 
    contextBundles,
    createPromptStrategy,
    addContextBundle,
    selectContextBundle,
    removeContextBundle,
    modelSettings
  } = useAppStore();

  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [selectedModel, setSelectedModel] = useState('internal');
  const [customPrompt, setCustomPrompt] = useState('');
  const [strategyName, setStrategyName] = useState('');
  const [newContextName, setNewContextName] = useState('');
  const [newContextContent, setNewContextContent] = useState('');
  const [showContextForm, setShowContextForm] = useState(false);

  const selectedContextBundles = contextBundles.filter(ctx => ctx.selected);
  const availableContextBundles = contextBundles.filter(ctx => !ctx.selected);

  const promptTemplates = [
    { id: 'analysis', name: 'Analysis Template', template: 'Analyze the following information and provide detailed insights:\n\n{context}\n\nPlease focus on:\n1. Key patterns and trends\n2. Potential implications\n3. Recommended actions' },
    { id: 'creative', name: 'Creative Template', template: 'Using the provided context as inspiration:\n\n{context}\n\nGenerate creative solutions that:\n1. Address the core challenge\n2. Think outside conventional approaches\n3. Consider innovative possibilities' },
    { id: 'problem-solving', name: 'Problem Solving', template: 'Given this problem context:\n\n{context}\n\nProvide a structured solution that includes:\n1. Problem definition\n2. Root cause analysis\n3. Solution options with pros/cons\n4. Implementation plan' },
    { id: 'strategic', name: 'Strategic Planning', template: 'Based on the strategic context:\n\n{context}\n\nDevelop a strategic approach covering:\n1. Current situation assessment\n2. Strategic objectives\n3. Key initiatives\n4. Success metrics' }
  ];

  const generatePromptPreview = () => {
    const template = promptTemplates.find(t => t.id === selectedTemplate);
    if (!template) return customPrompt || 'Select a template or write a custom prompt...';

    const contextText = selectedContextBundles.length > 0 
      ? selectedContextBundles.map(ctx => `${ctx.name}: ${ctx.content}`).join('\n\n')
      : '[Context will be injected here]';

    return template.template.replace('{context}', contextText);
  };

  const handleSaveStrategy = () => {
    if (!strategyName.trim()) {
      alert('Please enter a strategy name');
      return;
    }

    const template = promptTemplates.find(t => t.id === selectedTemplate);
    createPromptStrategy({
      name: strategyName,
      queryStructure: selectedTemplate || 'custom',
      contextDependencies: selectedContextBundles.map(ctx => ctx.id),
      targetModel: selectedModel,
      template: template?.template || customPrompt
    });

    setStrategyName('');
    setSelectedTemplate('');
    setCustomPrompt('');
  };

  const handleAddContext = () => {
    if (!newContextName.trim() || !newContextContent.trim()) {
      alert('Please enter both context name and content');
      return;
    }

    addContextBundle({
      name: newContextName,
      content: newContextContent,
      type: 'user-input',
      weight: 1.0,
      selected: false
    });

    setNewContextName('');
    setNewContextContent('');
    setShowContextForm(false);
  };

  return (
    <div className="space-y-4">
      {/* Prompt Engineering Header */}
      <Card className="p-4 border-purple-200 bg-purple-50 dark:bg-purple-950/20 dark:border-purple-800">
        <div className="flex items-center gap-2 mb-3">
          <Brain className="w-6 h-6 text-purple-600" />
          <h3 className="font-semibold text-purple-900 dark:text-purple-100">Prompt/Context Engineering AI</h3>
        </div>
        
        {/* Strategy Builder */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* Query Structure */}
            <div>
              <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Query Structure</Label>
              <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  {promptTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom Structure</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Context Dependencies */}
            <div>
              <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Context Dependencies</Label>
              <Button 
                variant="outline" 
                className="w-full mt-1 justify-start" 
                size="sm"
                onClick={() => setShowContextForm(!showContextForm)}
              >
                <Database className="w-4 h-4 mr-1" />
                Manage Context ({contextBundles.length})
              </Button>
            </div>
            
            {/* Target Model */}
            <div>
              <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Target AI Model</Label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal">Internal (Context-aware)</SelectItem>
                  <SelectItem value="gpt-4">External: GPT-4</SelectItem>
                  <SelectItem value="claude">External: Claude</SelectItem>
                  <SelectItem value="gemini">External: Gemini</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Custom Prompt Area (when custom template selected) */}
          {selectedTemplate === 'custom' && (
            <div>
              <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Custom Prompt Template</Label>
              <Textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="Write your custom prompt template here. Use {context} to indicate where context should be injected..."
                className="mt-1 min-h-[100px]"
              />
            </div>
          )}
          
          {/* Context Management Form */}
          {showContextForm && (
            <Card className="p-3 bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">Add Context Bundle</h4>
                  <Button variant="ghost" size="sm" onClick={() => setShowContextForm(false)}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs">Context Name</Label>
                    <Input
                      value={newContextName}
                      onChange={(e) => setNewContextName(e.target.value)}
                      placeholder="e.g., Project Requirements"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Context Content</Label>
                    <Textarea
                      value={newContextContent}
                      onChange={(e) => setNewContextContent(e.target.value)}
                      placeholder="Enter the context content..."
                      className="mt-1 h-20"
                    />
                  </div>
                </div>
                <Button size="sm" onClick={handleAddContext}>
                  <Plus className="w-3 h-3 mr-1" />
                  Add Context
                </Button>
              </div>
            </Card>
          )}
          
          {/* Context Injection Panel */}
          <div>
            <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Context Injection</Label>
            <div className="mt-1 p-3 bg-white dark:bg-gray-800 rounded-lg border border-purple-200 dark:border-purple-700 min-h-[80px]">
              {selectedContextBundles.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                  <Database className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No context selected</p>
                  <p className="text-xs">Select context bundles below to inject into prompts</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {selectedContextBundles.map(ctx => (
                    <div key={ctx.id} className="flex items-center gap-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded border">
                      <Badge variant="secondary" className="text-xs">
                        {ctx.type}
                      </Badge>
                      <span className="flex-1 text-sm font-medium">{ctx.name}</span>
                      <div className="flex items-center gap-1">
                        <Weight className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{ctx.weight}</span>
                      </div>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        onClick={() => selectContextBundle(ctx.id, false)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Available Context */}
              {availableContextBundles.length > 0 && (
                <div className="mt-3 pt-3 border-t border-purple-200 dark:border-purple-700">
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Available Context:</p>
                  <div className="flex flex-wrap gap-1">
                    {availableContextBundles.map(ctx => (
                      <Button
                        key={ctx.id}
                        size="sm"
                        variant="outline"
                        onClick={() => selectContextBundle(ctx.id, true)}
                        className="h-6 text-xs"
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        {ctx.name}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Generated Prompt Preview */}
          <div>
            <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Generated Prompt Preview</Label>
            <div className="mt-1 p-3 bg-white dark:bg-gray-800 rounded-lg border border-purple-200 dark:border-purple-700 font-mono text-sm max-h-40 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-gray-900 dark:text-gray-100">
                {generatePromptPreview()}
              </pre>
            </div>
          </div>
          
          {/* Strategy Management */}
          <div className="flex items-center gap-2 pt-2 border-t border-purple-200 dark:border-purple-700">
            <Input
              value={strategyName}
              onChange={(e) => setStrategyName(e.target.value)}
              placeholder="Strategy name..."
              className="flex-1"
            />
            <Button size="sm" onClick={handleSaveStrategy}>
              <Save className="w-3 h-3 mr-1" />
              Save Strategy
            </Button>
          </div>
          
          {/* Actions */}
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              <Play className="w-3 h-3 mr-1" />
              Test Prompt
            </Button>
            <Button size="sm" variant="outline">
              <Send className="w-3 h-3 mr-1" />
              Send to AI Exec
            </Button>
            <Button size="sm" variant="outline">
              <Eye className="w-3 h-3 mr-1" />
              Preview Context
            </Button>
          </div>
          
          {/* Saved Strategies */}
          {promptStrategies.length > 0 && (
            <div>
              <Label className="text-sm font-medium text-purple-900 dark:text-purple-100">Saved Strategies</Label>
              <div className="mt-1 space-y-1 max-h-24 overflow-y-auto">
                {promptStrategies.map(strategy => (
                  <div key={strategy.id} className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border text-sm">
                    <Settings className="w-3 h-3 text-purple-600" />
                    <span className="flex-1 font-medium">{strategy.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {strategy.targetModel}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {strategy.createdAt.toLocaleDateString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default PromptEngineeringInterface;