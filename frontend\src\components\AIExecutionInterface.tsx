import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Settings, Play, Pause, Square, Shield, ShieldOff, Terminal, Clock, DollarSign } from 'lucide-react';
import useAppStore from '../stores/useAppStore';

const AIExecutionInterface: React.FC = () => {
  const { 
    executionQueue, 
    goalShieldingEnabled, 
    toggleGoalShielding,
    addExecutionTask,
    updateExecutionTask,
    clearExecutionQueue,
    isLoading
  } = useAppStore();

  const queuedTasks = executionQueue.filter(task => task.status === 'queued').length;
  const processingTasks = executionQueue.filter(task => task.status === 'processing').length;
  const completedTasks = executionQueue.filter(task => task.status === 'completed').length;
  const totalCost = executionQueue.reduce((sum, task) => sum + task.cost, 0);

  const currentExecution = executionQueue.find(task => task.status === 'processing');

  return (
    <div className="space-y-4">
      {/* AI Execution Header */}
      <Card className="p-4 border-green-200 bg-green-50 dark:bg-green-950/20 dark:border-green-800">
        <div className="flex items-center gap-2 mb-3">
          <Settings className="w-6 h-6 text-green-600" />
          <h3 className="font-semibold text-green-900 dark:text-green-100">Advanced AI Execution</h3>
          <Badge variant={isLoading.advancedExecution ? "default" : "secondary"}>
            {isLoading.advancedExecution ? 'Processing' : 'Ready'}
          </Badge>
        </div>
        
        {/* Execution Status */}
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="text-2xl font-bold text-blue-600">{queuedTasks}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Queued</div>
            </div>
            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="text-2xl font-bold text-green-600">{completedTasks}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Completed</div>
            </div>
            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="text-2xl font-bold text-orange-600">${totalCost.toFixed(3)}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Cost</div>
            </div>
          </div>
          
          {/* Current Execution */}
          {currentExecution && (
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-green-900 dark:text-green-100">Current Execution</span>
                <Badge variant="default">
                  {currentExecution.model}
                </Badge>
              </div>
              <Progress value={currentExecution.progress} className="mb-2" />
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>Processing...</span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  <span>${currentExecution.cost.toFixed(3)}</span>
                </div>
              </div>
            </div>
          )}
          
          {/* Goal Shielding Control */}
          <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
            <div>
              <Label className="font-medium text-green-900 dark:text-green-100">Goal Shielding</Label>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Hide ultimate project goal from current prompt execution
              </p>
            </div>
            <div className="flex items-center gap-2">
              {goalShieldingEnabled ? (
                <Shield className="w-4 h-4 text-orange-600" />
              ) : (
                <ShieldOff className="w-4 h-4 text-gray-400" />
              )}
              <Switch 
                checked={goalShieldingEnabled}
                onCheckedChange={toggleGoalShielding}
              />
            </div>
          </div>
          
          {/* Execution Controls */}
          <div className="flex gap-2 pt-2 border-t border-green-200 dark:border-green-700">
            <Button size="sm" disabled={queuedTasks === 0}>
              <Play className="w-3 h-3 mr-1" />
              Execute Queue
            </Button>
            <Button size="sm" variant="outline" disabled={!isLoading.advancedExecution}>
              <Pause className="w-3 h-3 mr-1" />
              Pause
            </Button>
            <Button size="sm" variant="outline" disabled={!isLoading.advancedExecution}>
              <Square className="w-3 h-3 mr-1" />
              Stop
            </Button>
            <Button size="sm" variant="outline" onClick={clearExecutionQueue} disabled={executionQueue.length === 0}>
              <Terminal className="w-3 h-3 mr-1" />
              Clear Queue
            </Button>
          </div>
          
          {/* Execution Queue */}
          {executionQueue.length > 0 && (
            <div>
              <Label className="text-sm font-medium text-green-900 dark:text-green-100">Execution Queue</Label>
              <div className="mt-1 space-y-1 max-h-32 overflow-y-auto">
                {executionQueue.slice(0, 5).map(task => (
                  <div key={task.id} className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border text-sm">
                    <Badge 
                      variant={
                        task.status === 'completed' ? 'default' :
                        task.status === 'processing' ? 'secondary' :
                        task.status === 'failed' ? 'destructive' :
                        'outline'
                      }
                      className="text-xs"
                    >
                      {task.status}
                    </Badge>
                    <span className="flex-1 font-medium">{task.model}</span>
                    <span className="text-xs text-gray-500">
                      ${task.cost.toFixed(3)}
                    </span>
                    {task.status === 'processing' && (
                      <div className="w-12">
                        <Progress value={task.progress} className="h-1" />
                      </div>
                    )}
                  </div>
                ))}
                {executionQueue.length > 5 && (
                  <div className="text-xs text-gray-500 text-center py-1">
                    +{executionQueue.length - 5} more tasks
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Model Selection */}
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">Internal Model</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Context-aware • Goal shielded</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Memory-rich processing</div>
            </div>
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700">
              <div className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">External Models</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">GPT-4 • Claude • Gemini</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Markdown import supported</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AIExecutionInterface;