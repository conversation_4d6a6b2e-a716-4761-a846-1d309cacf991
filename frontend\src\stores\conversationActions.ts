
import { AppState, Conversation, Message } from './types';

export const createDefaultConversation = (): Conversation => ({
  id: Date.now().toString(),
  name: 'New Conversation',
  createdAt: new Date(),
  isContextShielded: false,
  messages: {
    core: [],
    plan: [],
  },
});

export interface ConversationActions {
  createNewConversation: () => void;
  createBranchedConversation: (title: string) => void;
  createWorkspaceConversation: (workspaceName: string, workspaceDescription: string) => void;
  switchConversation: (conversationId: string) => void;
  renameConversation: (conversationId: string, newName: string) => void;
  deleteConversation: (conversationId: string) => void;
  toggleContextShield: (conversationId: string) => void;
  getActiveConversation: () => Conversation | undefined;
  getAllMessages: () => Message[];
  getVisibleMessages: () => Message[];
  getCoreContextForPlan: () => Message[];
}

export const createConversationActions = (
  set: (partial: Partial<AppState> | ((state: AppState) => Partial<AppState>)) => void,
  get: () => AppState
): ConversationActions => {
  const getActiveConversation = (): Conversation | undefined => {
    const state = get();
    if (!state.activeConversationId && state.conversations.length > 0) {
      // Auto-select first conversation if none is active
      set({ activeConversationId: state.conversations[0].id });
      return state.conversations[0];
    }
    return state.conversations.find(conv => conv.id === state.activeConversationId);
  };

  return {
    createNewConversation: () => {
      const newConversation = createDefaultConversation();
      set((state) => ({
        conversations: [...state.conversations, newConversation],
        activeConversationId: newConversation.id,
      }));
    },

    createBranchedConversation: (title: string) => {
      const activeConversation = getActiveConversation();
      if (!activeConversation) return;

      const branchedConversation: Conversation = {
        id: Date.now().toString(),
        name: title,
        createdAt: new Date(),
        parentId: activeConversation.id,
        isContextShielded: false,
        messages: {
          core: [...activeConversation.messages.core], // Copy existing core messages
          plan: [], // Start fresh for plan messages in branch
        },
      };

      set((state) => ({
        conversations: [...state.conversations, branchedConversation],
        activeConversationId: branchedConversation.id,
        isBranchModalOpen: false,
        branchTitle: '',
      }));
    },

    createWorkspaceConversation: (workspaceName: string, workspaceDescription: string) => {
      const newConversation = {
        ...createDefaultConversation(),
        name: workspaceName
      };
      
      set((state) => ({
        conversations: [...state.conversations, newConversation],
        activeConversationId: newConversation.id,
        currentWorkspace: { name: workspaceName, description: workspaceDescription },
      }));

      // Data will be populated via backend API
    },

    switchConversation: (conversationId) => {
      set({ activeConversationId: conversationId });
    },

    renameConversation: (conversationId, newName) => {
      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId ? { ...conv, name: newName } : conv
        )
      }));
    },

    deleteConversation: (conversationId) => {
      set((state) => {
        const filteredConversations = state.conversations.filter(conv => conv.id !== conversationId);
        
        // If we're deleting the active conversation, switch to the first available one
        let newActiveId = state.activeConversationId;
        if (conversationId === state.activeConversationId) {
          newActiveId = filteredConversations.length > 0 ? filteredConversations[0].id : '';
        }

        // If no conversations left, create a new one
        if (filteredConversations.length === 0) {
          const newConversation = createDefaultConversation();
          return {
            conversations: [newConversation],
            activeConversationId: newConversation.id,
          };
        }

        return {
          conversations: filteredConversations,
          activeConversationId: newActiveId,
        };
      });
    },

    toggleContextShield: (conversationId) => {
      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId ? { ...conv, isContextShielded: !conv.isContextShielded } : conv
        )
      }));
    },

    getActiveConversation,

    getAllMessages: () => {
      const state = get();
      const allMessages: Message[] = [];
      
      state.conversations.forEach(conv => {
        allMessages.push(...conv.messages.core, ...conv.messages.plan);
      });
      
      return allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    },

    getVisibleMessages: () => {
      const state = get();
      const allMessages: Message[] = [];
      
      state.conversations.forEach(conv => {
        if (!conv.isContextShielded) {
          allMessages.push(...conv.messages.core, ...conv.messages.plan);
        }
      });
      
      return allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    },

    getCoreContextForPlan: () => {
      const activeConversation = getActiveConversation();
      if (!activeConversation || activeConversation.isContextShielded) {
        return [];
      }
      return activeConversation.messages.core;
    },
  };
};
