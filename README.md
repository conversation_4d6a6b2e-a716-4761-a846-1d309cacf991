# 🧠 ThoughtSyncApp
*AI-Powered Collaborative Workflow Platform*

[![CI/CD Pipeline](https://github.com/your-org/thoughtsync/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/your-org/thoughtsync/actions/workflows/ci-cd.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

## 🎯 Overview

ThoughtSyncApp is a sophisticated AI-powered collaborative platform that orchestrates multiple specialized AI agents to enhance productivity and decision-making. Built with modern web technologies and production-ready infrastructure.

### ✨ Key Features

- **🤖 Multi-Agent AI System**: Specialized AI agents for different workflow stages
- **🔄 Real-time Collaboration**: Live synchronization across team members
- **🧠 Context-Aware Intelligence**: Vector-powered semantic understanding
- **🛡️ Enterprise Security**: Comprehensive security and privacy controls
- **📊 Advanced Analytics**: Real-time monitoring and insights
- **🚀 Production Ready**: Scalable infrastructure with automated deployment

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **UI Library**: Shadcn/ui with Tailwind CSS
- **State Management**: Zustand with persistent storage
- **Real-time**: Socket.io client integration

### Backend (Node.js + Express)
- **Runtime**: Node.js 20+ with TypeScript
- **Framework**: Express.js with comprehensive middleware
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis for sessions and caching
- **AI Integration**: OpenAI, Google AI, and vector databases

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Docker Compose for development and production
- **Load Balancing**: Nginx with SSL termination
- **Monitoring**: Prometheus + Grafana stack
- **CI/CD**: GitHub Actions with automated testing

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 20+ (for development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/thoughtsync.git
   cd thoughtsync
   ```

2. **Set up environment variables**
   ```bash
   # Copy the example environment file
   cp backend/.env.example backend/.env
   ```

   **Edit `backend/.env` with your configuration:**
   ```bash
   # Database Configuration
   DATABASE_URL="postgresql://username:password@localhost:5432/thoughtsync_db"
   
   # JWT Authentication
   JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
   JWT_EXPIRES_IN="7d"
   
   # Server Configuration
   PORT=3001
   NODE_ENV="development"
   FRONTEND_URL="http://localhost:8080"
   
   # Redis Cache (optional for development)
   REDIS_URL="redis://localhost:6379"
   
   # AI API Keys (Required)
   OPENAI_API_KEY="sk-your-openai-api-key-here"
   GOOGLE_API_KEY="your-google-ai-api-key-here"
   ANTHROPIC_API_KEY="your-anthropic-api-key-here"
   OPENROUTER_API_KEY="your-openrouter-api-key-here"
   
   # Vector Database (Required for AI context)
   PINECONE_API_KEY="your-pinecone-api-key-here"
   PINECONE_ENVIRONMENT="your-pinecone-environment"
   PINECONE_INDEX_NAME="thoughtsync-context"
   
   # Rate Limiting
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   ```

3. **Start development environment**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:8080
   - Backend API: http://localhost:3001
   - Health Check: http://localhost:3001/health

### Production Deployment

1. **Set up production environment**
   ```bash
   ./scripts/setup-production.sh
   ```

2. **Configure environment**
   ```bash
   # Edit .env.production with your production values
   nano .env.production
   ```

3. **Deploy to production**
   ```bash
   ./scripts/deploy.sh production
   ```

4. **Access production application**
   - Application: https://your-domain.com
   - Monitoring: https://your-domain.com:3000

## 🧬 AI Workflow System

### Specialized AI Agents

1. **🧭 Project Navigator AI**
   - Project definition and goal setting
   - Real-time project updates and pivots
   - Milestone tracking and success criteria

2. **🧠 Prompt Engineering AI**
   - Context curation and prompt optimization
   - Strategy development for AI interactions
   - Context dependency management

3. **⚙️ Advanced Execution AI**
   - Multi-model AI processing
   - Tool integration and execution
   - Response optimization and routing

4. **✍️ Summarizer AI**
   - Insight extraction and condensation
   - Pattern recognition across conversations
   - Knowledge synthesis and tagging

5. **♻️ Iterative Loop AI**
   - Workflow orchestration and optimization
   - Gap analysis and contradiction detection
   - Continuous improvement recommendations

### Workflow Process

```mermaid
graph TD
    A[Project Definition] --> B[Prompt Strategy]
    B --> C[AI Execution]
    C --> D[Summarization]
    D --> E[Iterative Analysis]
    E --> B
    E --> F[Goal Achievement]
```

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Session management with Redis

### Security Headers
- HSTS (HTTP Strict Transport Security)
- CSP (Content Security Policy)
- XSS Protection
- CSRF Protection

### Rate Limiting
- API endpoints: 100 requests/15min
- Auth endpoints: 5 requests/min
- AI endpoints: 20 requests/min

### Data Protection
- Input sanitization and validation
- SQL injection prevention
- XSS attack mitigation
- Secure cookie handling

## 📊 Monitoring & Observability

### Health Monitoring
- **Basic Health**: `GET /health`
- **Detailed Health**: `GET /health/detailed`
- **Readiness Probe**: `GET /ready`
- **Liveness Probe**: `GET /live`

### Metrics Collection
- Request/response time tracking
- Error rate monitoring
- Resource usage metrics
- Business intelligence metrics

### Monitoring Stack
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visual dashboards and analytics
- **Application Logs**: Structured logging with correlation IDs

## 🔧 Environment Variables

### Required Environment Variables

ThoughtSyncApp requires several environment variables to function properly. Here's a comprehensive guide:

#### **Database Configuration**
```bash
# PostgreSQL Database URL Format
DATABASE_URL="postgresql://username:password@host:port/database_name"

# Option 1: Docker (Recommended - Automatic Setup)
DATABASE_URL="postgresql://thoughtsync:thoughtsync_password@localhost:5432/thoughtsync_db"

# Option 2: Local PostgreSQL Installation
DATABASE_URL="postgresql://your_username:your_password@localhost:5432/thoughtsync_db"

# Option 3: Cloud Database (Production)
DATABASE_URL="**************************************************/thoughtsync_prod"
```

#### **Authentication & Security**
```bash
# JWT Secret (REQUIRED - Use a strong, random string)
JWT_SECRET="your-super-secure-jwt-secret-minimum-32-characters"
JWT_EXPIRES_IN="7d"

# Server Configuration
PORT=3001
NODE_ENV="development"  # or "production"
FRONTEND_URL="http://localhost:8080"  # Frontend URL for CORS
```

#### **AI Service API Keys** ⚠️ **REQUIRED**
```bash
# OpenAI (Required for embeddings and GPT models)
OPENAI_API_KEY="sk-your-openai-api-key-here"

# Google AI (Required for Gemini models)
GOOGLE_API_KEY="your-google-ai-api-key-here"

# Optional AI Services
ANTHROPIC_API_KEY="your-anthropic-api-key-here"
OPENROUTER_API_KEY="your-openrouter-api-key-here"
```

#### **Vector Database** ⚠️ **REQUIRED**
```bash
# Pinecone Configuration (Required for AI context management)
PINECONE_API_KEY="your-pinecone-api-key-here"
PINECONE_ENVIRONMENT="us-east-1-aws"  # Find this in your Pinecone dashboard
PINECONE_INDEX_NAME="thoughtsync-context"

# Common Pinecone environments:
# - us-east-1-aws (most common for free tier)
# - us-west1-gcp
# - eu-west1-aws
# - asia-southeast1-gcp
```

#### **Optional Services**
```bash
# Redis Cache (Optional - improves performance)
REDIS_URL="redis://localhost:6379"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000      # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100      # Max requests per window

# Monitoring (Production)
SENTRY_DSN="your-sentry-dsn-for-error-tracking"
```

### 🔑 How to Get API Keys

#### **OpenAI API Key**
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-`)

#### **Google AI API Key**
1. Visit [Google AI Studio](https://makersuite.google.com/)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key

#### **Pinecone API Key & Environment**
1. Visit [Pinecone](https://www.pinecone.io/)
2. Sign up for a free account
3. **Get your Environment:**
   - After logging in, go to your Pinecone dashboard
   - Look for your **Environment** in the top-right corner or project settings
   - Common environments: `us-east-1-aws`, `us-west1-gcp`, `eu-west1-aws`
   - Copy this exact value (e.g., `us-east-1-aws`)
4. **Get your API Key:**
   - In the dashboard, click on "API Keys" in the left sidebar
   - Copy your API key (starts with a long string of characters)
5. **Create a new index:**
   - Click "Create Index" or "Indexes" → "Create Index"
   - **Index Name**: `thoughtsync`
   - **Dimension**: `1536` (for OpenAI embeddings)
   - **Metric**: `cosine`
   - **Environment**: Select the same environment from step 3
   - Click "Create Index"

**Example values:**
```bash
PINECONE_API_KEY="********-1234-1234-1234-********9abc"
PINECONE_ENVIRONMENT="us-east-1-aws"  # or "us-west1-gcp", "eu-west1-aws"
PINECONE_INDEX_NAME="thoughtsync-context"
```

### 📝 Environment Setup Examples

#### **Development (.env)**
```bash
# Minimal development setup
DATABASE_URL="postgresql://thoughtsync:thoughtsync_password@localhost:5432/thoughtsync_db"
JWT_SECRET="dev-secret-key-change-in-production-minimum-32-chars"
OPENAI_API_KEY="sk-your-openai-key"
GOOGLE_API_KEY="your-google-key"
PINECONE_API_KEY="your-pinecone-key"
PINECONE_ENVIRONMENT="us-west1-gcp"
PINECONE_INDEX_NAME="thoughtsync-context"
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:8080"
```

#### **Production (.env.production)**
```bash
# Production configuration
DATABASE_URL="**********************************************/thoughtsync_prod"
JWT_SECRET="super-secure-production-jwt-secret-with-64-characters-minimum"
OPENAI_API_KEY="sk-prod-openai-key"
GOOGLE_API_KEY="prod-google-key"
PINECONE_API_KEY="prod-pinecone-key"
PINECONE_ENVIRONMENT="us-west1-gcp"
PINECONE_INDEX_NAME="thoughtsync-prod"
REDIS_URL="redis://prod-redis:6379"
NODE_ENV="production"
PORT=3001
FRONTEND_URL="https://your-domain.com"
SENTRY_DSN="https://your-sentry-dsn"
```

### ⚠️ Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use strong, unique JWT secrets** (minimum 32 characters)
3. **Rotate API keys regularly**
4. **Use different keys for development and production**
5. **Restrict API key permissions** where possible
6. **Monitor API usage** to detect unauthorized access

### 🔍 Environment Validation

The application will validate required environment variables on startup:

```bash
# Check if all required variables are set
npm run dev

# You'll see validation errors if any required variables are missing:
# ❌ Missing required environment variable: OPENAI_API_KEY
# ❌ Missing required environment variable: PINECONE_API_KEY
```

## 🛠️ Development

### Project Structure
```
thoughtsync/
├── backend/                 # Node.js API server
│   ├── src/
│   │   ├── routes/         # API endpoints
│   │   ├── middleware/     # Express middleware
│   │   ├── lib/           # Core libraries
│   │   └── config/        # Configuration
│   └── prisma/            # Database schema
├── frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── stores/        # State management
│   │   └── pages/         # Application pages
├── deployment/             # Production deployment
│   ├── nginx/             # Load balancer config
│   └── monitoring/        # Monitoring stack
└── scripts/               # Deployment scripts
```

### Available Scripts

#### Backend
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run db:migrate   # Run database migrations
```

#### Frontend
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run linting
```

#### Deployment
```bash
./scripts/setup-production.sh  # Initial production setup
./scripts/deploy.sh            # Deploy to production
./scripts/backup.sh            # Create backup
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Follow security guidelines
- Test deployment changes

## 📚 Documentation

- [Deployment Guide](docs/DEPLOYMENT.md) - Complete production deployment
- [API Documentation](docs/API.md) - Backend API reference
- [Architecture Guide](docs/ARCHITECTURE.md) - System architecture
- [Security Guide](docs/SECURITY.md) - Security best practices

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Check database status
docker-compose exec postgres pg_isready
```

**Service Health Issues**
```bash
# Check all services
docker-compose ps
# View logs
docker-compose logs -f service-name
```

**Performance Issues**
```bash
# Check metrics
curl http://localhost:3001/metrics
# Monitor resources
docker stats
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models and embeddings
- Google for Gemini AI integration
- Pinecone for vector database services
- The open-source community for amazing tools and libraries

---

**Built with ❤️ by the ThoughtSync Team**