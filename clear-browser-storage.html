<!DOCTYPE html>
<html>
<head>
    <title>Clear SynergyAI Storage - Docker Version</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            max-width: 800px; 
            margin: 0 auto; 
            background: #f5f5f5;
        }
        .container { 
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #333; 
            text-align: center;
            margin-bottom: 30px;
        }
        button { 
            padding: 15px 30px; 
            font-size: 16px; 
            background: #dc2626; 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 10px; 
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 10px auto;
        }
        button:hover { 
            background: #b91c1c; 
        }
        .success { 
            color: #16a34a; 
            margin-top: 20px; 
            text-align: center;
            font-weight: bold;
        }
        .info { 
            background: #f3f4f6; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid #3b82f6;
        }
        .warning {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .docker-info {
            background: #dbeafe;
            border-left: 4px solid #2563eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        ol, ul { 
            text-align: left; 
            max-width: 600px;
            margin: 0 auto;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear SynergyAI Storage (Docker Version)</h1>
        
        <div class="docker-info">
            <h3>🐳 For Docker Users</h3>
            <p>Since you're running SynergyAI in Docker containers, the mock data might be coming from multiple sources:</p>
            <ul>
                <li><strong>Browser Storage:</strong> Cached data in your browser</li>
                <li><strong>Docker Volumes:</strong> Database data persisted in Docker volumes</li>
                <li><strong>Container Images:</strong> Hardcoded data in the container images</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ Important</h3>
            <p>If clearing browser storage doesn't work, you'll need to run the Docker cleanup script to completely reset your containers and database.</p>
        </div>
        
        <div class="info">
            <p>This tool will clear all browser storage including workspaces, conversations, and settings.</p>
            <p><strong>Try this first</strong> before running the Docker cleanup script.</p>
        </div>
        
        <button onclick="clearStorage()">Clear Browser Storage Only</button>
        <button onclick="clearAndReload()">Clear Storage & Reload SynergyAI</button>
        
        <div id="result"></div>
        
        <div class="info">
            <h3>📋 Complete Docker Cleanup Process</h3>
            <div class="step">
                <strong>Step 1:</strong> Try clearing browser storage first (use buttons above)
            </div>
            <div class="step">
                <strong>Step 2:</strong> If mock data still appears, run the Docker cleanup script:
                <br><code>./clear-docker-data.bat</code> (Windows) or <code>./clear-docker-data.sh</code> (Linux/Mac)
            </div>
            <div class="step">
                <strong>Step 3:</strong> After Docker cleanup, clear browser storage again and refresh
            </div>
        </div>

        <div class="info">
            <h3>🔧 Manual Browser Cleanup</h3>
            <ol>
                <li>Open your SynergyAI application at <strong>http://localhost:3000</strong></li>
                <li>Open browser Developer Tools (F12)</li>
                <li>Go to <strong>Application</strong> tab (Chrome) or <strong>Storage</strong> tab (Firefox)</li>
                <li>Clear <strong>localStorage</strong>, <strong>sessionStorage</strong>, and <strong>IndexedDB</strong></li>
                <li>Hard refresh the page (Ctrl+F5 or Cmd+Shift+R)</li>
            </ol>
        </div>

        <div class="info">
            <h3>🚨 If Problems Persist</h3>
            <ul>
                <li>Try opening SynergyAI in <strong>incognito/private mode</strong></li>
                <li>Check browser console for errors (F12 > Console)</li>
                <li>Verify Docker containers are running: <code>docker-compose ps</code></li>
                <li>Check if backend is accessible: <strong>http://localhost:3001/health</strong></li>
            </ul>
        </div>
    </div>

    <script>
        function clearStorage() {
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear IndexedDB
                if ('indexedDB' in window) {
                    indexedDB.databases().then(databases => {
                        databases.forEach(db => {
                            if (db.name && (
                                db.name.includes('synergyai') || 
                                db.name.includes('thoughtsync') ||
                                db.name.includes('zustand')
                            )) {
                                indexedDB.deleteDatabase(db.name);
                            }
                        });
                    });
                }
                
                document.getElementById('result').innerHTML = 
                    '<div class="success">' +
                    '<h3>✅ Browser Storage Cleared!</h3>' +
                    '<p>Browser storage has been cleared. Now refresh your SynergyAI application at <strong>http://localhost:3000</strong></p>' +
                    '<p>If mock data still appears, you need to run the Docker cleanup script.</p>' +
                    '</div>';
                    
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: #dc2626; text-align: center;">' +
                    '<h3>❌ Error</h3>' +
                    '<p>Could not clear storage: ' + error.message + '</p>' +
                    '</div>';
            }
        }
        
        function clearAndReload() {
            clearStorage();
            setTimeout(() => {
                // Try to open SynergyAI in a new tab
                window.open('http://localhost:3000', '_blank');
            }, 2000);
        }
    </script>
</body>
</html>
