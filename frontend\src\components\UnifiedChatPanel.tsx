
import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import useAppStore from '../stores/useAppStore';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ChatActions from './ChatActions';
import BranchModal from './BranchModal';
import PromptDraftModal from './PromptDraftModal';
import { useToast } from '../hooks/use-toast';
import ChatHeader from './ChatHeader';
import { AGENT_MODES, getAgentPlaceholder, type AgentMode } from '../config/agentConfig';
import ProjectNavigatorInterface from './ProjectNavigatorInterface';
import PromptEngineeringInterface from './PromptEngineeringInterface';
import AIExecutionInterface from './AIExecutionInterface';

const UnifiedChatPanel: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [activeMode, setActiveMode] = useState<'project-navigator' | 'prompt-engineering' | 'ai-execution' | 'summarizer' | 'general'>('project-navigator');
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState('');
  const [isPromptDraftModalOpen, setIsPromptDraftModalOpen] = useState(false);
  const [isGeneratingDraft, setIsGeneratingDraft] = useState(false);
  const [generatedDraft, setGeneratedDraft] = useState('');
  const { toast } = useToast();
  
  const { 
    isLoading, 
    sendMessage, 
    sendGeneralChatMessage,
    clearGeneralChat,
    togglePinMessage, 
    getActiveConversation,
    generalChatMessages,
    deleteMessage,
    editMessage,
    refreshAssistantMessage,
    resendUserMessage,
    toggleBranchModal,
    toggleContextShield,
    getCoreContextForPlan
  } = useAppStore();
  
  const activeConversation = getActiveConversation();
  
  // Get messages based on active mode - Updated for workflow.md agents
  const getMessagesToDisplay = () => {
    if (activeMode === 'general') {
      return generalChatMessages.map(msg => ({ ...msg, mode: 'general' as const }));
    } else if (activeMode === 'project-navigator') {
      return activeConversation ? activeConversation.messages.core.map(msg => ({ ...msg, mode: 'project-navigator' as const })) : [];
    } else if (activeMode === 'prompt-engineering') {
      return activeConversation ? activeConversation.messages.plan.map(msg => ({ ...msg, mode: 'prompt-engineering' as const })) : [];
    } else if (activeMode === 'ai-execution') {
      // For now, use core messages for execution history
      return activeConversation ? activeConversation.messages.core.map(msg => ({ ...msg, mode: 'ai-execution' as const })) : [];
    } else if (activeMode === 'summarizer') {
      // For now, use plan messages for summarizer history  
      return activeConversation ? activeConversation.messages.plan.map(msg => ({ ...msg, mode: 'summarizer' as const })) : [];
    }
    return [];
  };

  const messagesToDisplay = getMessagesToDisplay();
  
  // Map new agent modes to loading states
  const getLoadingState = () => {
    switch (activeMode) {
      case 'project-navigator': return isLoading.projectNavigator;
      case 'prompt-engineering': return isLoading.promptEngineering;
      case 'ai-execution': return isLoading.advancedExecution;
      case 'summarizer': return isLoading.summarizer;
      case 'general': return isLoading.general;
      default: return false;
    }
  };
  
  const loading = getLoadingState();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !loading) {
      if (activeMode === 'general') {
        sendGeneralChatMessage(inputValue.trim());
      } else if (activeConversation) {
        // Map new agent modes to existing message system temporarily
        const messageMode = activeMode === 'project-navigator' ? 'core' :
                           activeMode === 'prompt-engineering' ? 'plan' :
                           activeMode === 'ai-execution' ? 'core' :
                           activeMode === 'summarizer' ? 'plan' : 'core';
        sendMessage(messageMode, inputValue.trim());
      }
      setInputValue('');
    }
  };

  const handleCopyMessage = () => {
    toast({
      title: "Copied!",
      description: "Message copied to clipboard",
    });
  };

  const handleEditMessage = (messageId: string, content: string) => {
    setEditingMessageId(messageId);
    setEditingContent(content);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingContent.trim()) {
      const messageMode = activeMode === 'project-navigator' ? 'core' :
                         activeMode === 'prompt-engineering' ? 'plan' :
                         activeMode === 'ai-execution' ? 'core' :
                         activeMode === 'summarizer' ? 'plan' : 'core';
      editMessage(messageMode, editingMessageId, editingContent.trim());
      setEditingMessageId(null);
      setEditingContent('');
      toast({
        title: "Updated!",
        description: "Message has been updated",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingContent('');
  };

  const handleBranchConversation = () => {
    if (activeMode === 'project-navigator' && activeConversation && activeConversation.messages.core.length > 0) {
      toggleBranchModal();
    } else {
      toast({
        title: "Cannot branch",
        description: "You can only branch from Project Navigator mode with existing messages",
        variant: "destructive"
      });
    }
  };

  const handleToggleContextShield = () => {
    if (activeConversation) {
      toggleContextShield(activeConversation.id);
      toast({
        title: activeConversation.isContextShielded ? "Context Unshielded" : "Context Shielded",
        description: activeConversation.isContextShielded 
          ? "This conversation is now visible to Compass & Plan AI"
          : "This conversation is now hidden from Compass & Plan AI"
      });
    }
  };

  const handleCreatePromptDraft = async () => {
    if (activeMode !== 'prompt-engineering' || !activeConversation) return;

    const planMessages = activeConversation.messages.plan;

    if (planMessages.length === 0) {
      toast({
        title: "No discussion yet",
        description: "Have a discussion with Prompt Engineering AI first to generate a prompt draft.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingDraft(true);
    setGeneratedDraft(''); // Clear previous draft

    // Simulate AI call to generate prompt draft.
    // In a real application, this would be an API call to a language model.
    try {
      // Generate prompt draft based on conversation context
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate network delay
      
      const lastUserMessage = [...planMessages].reverse().find(m => m.role === 'user');
      const generatedPrompt = lastUserMessage 
        ? `Based on our planning discussion, please perform the following analysis: ${lastUserMessage.content}`
        : "Based on our planning discussion, please perform a detailed analysis.";

      setGeneratedDraft(generatedPrompt);
      setIsPromptDraftModalOpen(true);
    } catch (error) {
      console.error("Failed to generate prompt draft", error);
      toast({
        title: "Error",
        description: "Could not generate prompt draft.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingDraft(false);
    }
  };

  const getCoreContextInfo = () => {
    if (activeMode === 'plan') {
      const coreMessages = getCoreContextForPlan();
      return coreMessages.length > 0 ? `${coreMessages.length} Core messages available` : 'No Core context available';
    }
    return '';
  };

  if (!activeConversation && activeMode !== 'general') {
    return (
      <div className="flex flex-col h-full bg-card border border-border rounded-2xl overflow-hidden shadow-soft">
        <div className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center px-8">
            <div className="w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-6">
              <MessageSquare className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-3">No conversation selected</h3>
            <p className="text-base text-muted-foreground">Create a new conversation to get started</p>
          </div>
        </div>
        <BranchModal />
        <PromptDraftModal 
          isOpen={isPromptDraftModalOpen} 
          onClose={() => setIsPromptDraftModalOpen(false)} 
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-card border border-border rounded-2xl overflow-hidden shadow-soft">
      <div className="bg-card px-4 py-3 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChatHeader
              activeMode={activeMode}
              setActiveMode={setActiveMode}
              activeConversation={activeConversation}
              generalChatMessagesCount={generalChatMessages.length}
              coreMessagesCount={activeConversation?.messages.core.length ?? 0}
              planMessagesCount={activeConversation?.messages.plan.length ?? 0}
              getCoreContextInfo={getCoreContextInfo}
              handleCreatePromptDraft={handleCreatePromptDraft}
              isGeneratingDraft={isGeneratingDraft}
              handleToggleContextShield={handleToggleContextShield}
              handleBranchConversation={handleBranchConversation}
              clearGeneralChat={clearGeneralChat}
            />
          </div>
          <ChatActions
            activeMode={activeMode}
            activeConversation={activeConversation}
          />
        </div>
      </div>

      {/* Agent-specific interfaces */}
      {activeMode === 'project-navigator' && (
        <div className="border-b border-border p-4 max-h-64 overflow-y-auto">
          <ProjectNavigatorInterface />
        </div>
      )}
      
      {activeMode === 'prompt-engineering' && (
        <div className="border-b border-border p-4 max-h-80 overflow-y-auto">
          <PromptEngineeringInterface />
        </div>
      )}
      
      {activeMode === 'ai-execution' && (
        <div className="border-b border-border p-4 max-h-64 overflow-y-auto">
          <AIExecutionInterface />
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
        <MessageList
          messages={messagesToDisplay}
          activeMode={activeMode}
          isLoading={loading}
          editingMessageId={editingMessageId}
          editingContent={editingContent}
          setEditingContent={setEditingContent}
          onTogglePin={(messageId) => {
            const messageMode = activeMode === 'project-navigator' ? 'core' :
                             activeMode === 'prompt-engineering' ? 'plan' :
                             activeMode === 'ai-execution' ? 'core' :
                             activeMode === 'summarizer' ? 'plan' : 'core';
            togglePinMessage(messageMode, messageId);
          }}
          onCopyMessage={handleCopyMessage}
          onRefreshMessage={(messageId) => {
            const messageMode = activeMode === 'project-navigator' ? 'core' :
                             activeMode === 'prompt-engineering' ? 'plan' :
                             activeMode === 'ai-execution' ? 'core' :
                             activeMode === 'summarizer' ? 'plan' : 'core';
            refreshAssistantMessage(messageMode, messageId);
          }}
          onDeleteMessage={(messageId) => {
            const messageMode = activeMode === 'project-navigator' ? 'core' :
                             activeMode === 'prompt-engineering' ? 'plan' :
                             activeMode === 'ai-execution' ? 'core' :
                             activeMode === 'summarizer' ? 'plan' : 'core';
            deleteMessage(messageMode, messageId);
          }}
          onEditMessage={handleEditMessage}
          onResendMessage={(messageId) => {
            const messageMode = activeMode === 'project-navigator' ? 'core' :
                             activeMode === 'prompt-engineering' ? 'plan' :
                             activeMode === 'ai-execution' ? 'core' :
                             activeMode === 'summarizer' ? 'plan' : 'core';
            resendUserMessage(messageMode, messageId);
          }}
          onSaveEdit={handleSaveEdit}
          onCancelEdit={handleCancelEdit}
        />
      </div>

      <ChatInput
        inputValue={inputValue}
        setInputValue={setInputValue}
        activeMode={activeMode}
        isLoading={loading}
        isContextShielded={activeConversation?.isContextShielded}
        onSubmit={handleSubmit}
        placeholder={getAgentPlaceholder(activeMode as AgentMode)}
      />

      <BranchModal />
      <PromptDraftModal 
        isOpen={isPromptDraftModalOpen} 
        onClose={() => setIsPromptDraftModalOpen(false)} 
        initialPrompt={generatedDraft}
      />
    </div>
  );
};

export default UnifiedChatPanel;
