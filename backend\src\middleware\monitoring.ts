import { Request, Response, NextFunction } from 'express';
import { performance } from 'perf_hooks';

// Performance monitoring middleware
export interface PerformanceMetrics {
  requestCount: number;
  totalResponseTime: number;
  averageResponseTime: number;
  errorCount: number;
  slowRequestCount: number;
  endpoints: Map<string, {
    count: number;
    totalTime: number;
    averageTime: number;
    errors: number;
  }>;
}

class MonitoringService {
  private metrics: PerformanceMetrics = {
    requestCount: 0,
    totalResponseTime: 0,
    averageResponseTime: 0,
    errorCount: 0,
    slowRequestCount: 0,
    endpoints: new Map()
  };

  private readonly SLOW_REQUEST_THRESHOLD = 1000; // 1 second

  public performanceMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = performance.now();
      const endpoint = `${req.method} ${req.route?.path || req.path}`;

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = function(this: Response, ...args: any[]) {
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        // Update global metrics
        monitoringService.updateMetrics(endpoint, responseTime, res.statusCode >= 400);

        // Log slow requests
        if (responseTime > monitoringService.SLOW_REQUEST_THRESHOLD) {
          console.warn(`Slow request detected: ${endpoint} took ${responseTime.toFixed(2)}ms`);
        }

        return originalEnd.apply(this, args);
      };

      next();
    };
  }

  private updateMetrics(endpoint: string, responseTime: number, isError: boolean) {
    // Update global metrics
    this.metrics.requestCount++;
    this.metrics.totalResponseTime += responseTime;
    this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.requestCount;

    if (isError) {
      this.metrics.errorCount++;
    }

    if (responseTime > this.SLOW_REQUEST_THRESHOLD) {
      this.metrics.slowRequestCount++;
    }

    // Update endpoint-specific metrics
    const endpointMetrics = this.metrics.endpoints.get(endpoint) || {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      errors: 0
    };

    endpointMetrics.count++;
    endpointMetrics.totalTime += responseTime;
    endpointMetrics.averageTime = endpointMetrics.totalTime / endpointMetrics.count;

    if (isError) {
      endpointMetrics.errors++;
    }

    this.metrics.endpoints.set(endpoint, endpointMetrics);
  }

  public getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      endpoints: new Map(this.metrics.endpoints)
    };
  }

  public getHealthStatus() {
    const errorRate = this.metrics.requestCount > 0 
      ? (this.metrics.errorCount / this.metrics.requestCount) * 100 
      : 0;
    
    const slowRequestRate = this.metrics.requestCount > 0
      ? (this.metrics.slowRequestCount / this.metrics.requestCount) * 100
      : 0;

    return {
      status: errorRate < 5 && slowRequestRate < 10 ? 'healthy' : 'degraded',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      errorRate: errorRate.toFixed(2) + '%',
      slowRequestRate: slowRequestRate.toFixed(2) + '%',
      averageResponseTime: this.metrics.averageResponseTime.toFixed(2) + 'ms',
      totalRequests: this.metrics.requestCount,
      timestamp: new Date().toISOString()
    };
  }

  public resetMetrics() {
    this.metrics = {
      requestCount: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      errorCount: 0,
      slowRequestCount: 0,
      endpoints: new Map()
    };
  }
}

export const monitoringService = new MonitoringService();

// Health check middleware
export const healthCheck = (req: Request, res: Response) => {
  const health = monitoringService.getHealthStatus();
  
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
};

// Metrics endpoint middleware
export const metricsEndpoint = (req: Request, res: Response) => {
  const metrics = monitoringService.getMetrics();
  
  // Convert Map to object for JSON serialization
  const endpointsObj = Object.fromEntries(metrics.endpoints);
  
  res.json({
    ...metrics,
    endpoints: endpointsObj
  });
};

// Error tracking middleware
export const errorTracking = (error: Error, req: Request, res: Response, next: NextFunction) => {
  // Log error details
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });

  // In production, you would send this to an error tracking service like Sentry
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error);
  }

  next(error);
};