
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import ChatMessage from './ChatMessage';
import useAppStore from '@/stores/useAppStore';
import type { Message } from '@/stores/useAppStore';

interface ChatPanelProps {
  agent: 'core' | 'plan';
}

const ChatPanel: React.FC<ChatPanelProps> = ({ agent }) => {
  const { getActiveConversation, sendMessage, togglePinMessage, isLoading } = useAppStore();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const activeConversation = getActiveConversation();
  const messages: Message[] = activeConversation ? activeConversation.messages[agent] : [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = () => {
    if (input.trim() && !isLoading[agent]) {
      sendMessage(agent, input);
      setInput('');
    }
  };

  const handlePin = (messageId: string) => {
    togglePinMessage(agent, messageId);
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="p-4">
          {messages.map((msg) => (
            <ChatMessage key={msg.id} message={msg} onPin={() => handlePin(msg.id)} />
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <div className="flex items-center p-4 border-t">
        <Input
          type="text"
          placeholder={`Message ${agent}...`}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSend()}
          disabled={isLoading[agent]}
        />
        <Button onClick={handleSend} className="ml-2" disabled={isLoading[agent]}>
          {isLoading[agent] ? 'Thinking...' : 'Send'}
        </Button>
      </div>
    </div>
  );
};

export default ChatPanel;
