import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeHighlight from 'rehype-highlight';
import { RotateCw, FileText, Download, CheckCircle, AlertTriangle, Target, HelpCircle, Eye, Tags, BarChart3 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import useAppStore from '../stores/useAppStore';

const SummarizerPanel: React.FC = () => {
  const { 
    summarizerInsights, 
    condensedSummary, 
    isLoading, 
    updateCondensedSummary,
    updateSummarizerInsights 
  } = useAppStore();
  
  const { toast } = useToast();
  const loading = isLoading.summarizer;

  const downloadInsights = () => {
    // Create comprehensive markdown content
    const markdownContent = `# Summarizer AI Analysis & Insights

## SynergyAI Summary
${condensedSummary}

## Key Findings
${summarizerInsights.keyFindings.map(finding => `- ${finding.text} (Confidence: ${finding.confidence}%)`).join('\n')}

## Contradictions
${summarizerInsights.contradictions.map(contradiction => `- ${contradiction.text} (Severity: ${contradiction.severity})`).join('\n')}

## Promising Leads
${summarizerInsights.promisingLeads.map(lead => `- ${lead.text} (Priority: ${lead.priority})`).join('\n')}

## Theme Tags
${summarizerInsights.themeTags.map(tag => `- ${tag.name} (${tag.count} occurrences)`).join('\n')}

## Variables
${summarizerInsights.variables.map(variable => `- **${variable.name}**: ${variable.description} (Impact: ${variable.impact}, Confidence: ${variable.confidence}%)`).join('\n')}

## Open Questions
${summarizerInsights.openQuestions.map(question => `- ${question.text} (Category: ${question.category}, Priority: ${question.priority})`).join('\n')}

---
*Generated by SynergyAI on ${new Date().toLocaleDateString()}*
`;

    // Create and download file
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `synergyai-insights-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Insights Downloaded",
      description: "Your analysis has been saved as a markdown file.",
    });
  };

  const generateInsights = () => {
    // TODO: Implement actual AI insight generation
    toast({
      title: "Feature Coming Soon",
      description: "AI insight generation will be available in a future update.",
    });
  };

  return (
    <div className="space-y-4">
      {/* Summarizer Header */}
      <Card className="p-4 border-purple-200 bg-purple-50 dark:bg-purple-950/20 dark:border-purple-800">
        <div className="flex items-center gap-2 mb-3">
          <BarChart3 className="w-6 h-6 text-purple-600" />
          <h3 className="font-semibold text-purple-900 dark:text-purple-100">Summarizer AI</h3>
          <Badge variant={loading ? "default" : "secondary"}>
            {loading ? 'Analyzing' : 'Ready'}
          </Badge>
        </div>
        
        <div className="flex gap-2 mb-4">
          <Button 
            size="sm" 
            onClick={generateInsights}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {loading ? (
              <>
                <RotateCw className="w-3 h-3 mr-1 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <BarChart3 className="w-3 h-3 mr-1" />
                Generate Insights
              </>
            )}
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={downloadInsights}
            disabled={!condensedSummary && summarizerInsights.keyFindings.length === 0}
          >
            <Download className="w-3 h-3 mr-1" />
            Export
          </Button>
        </div>

        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summary" className="mt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">SynergyAI Summary</h3>
                <Button size="sm" variant="ghost">
                  <Eye className="w-3 h-3 mr-1" />
                  View Full
                </Button>
              </div>
              
              {condensedSummary ? (
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown rehypePlugins={[rehypeHighlight]}>
                    {condensedSummary}
                  </ReactMarkdown>
                </div>
              ) : (
                <div className="text-sm text-gray-500 italic p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  No summary available. Generate insights to create a condensed summary.
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="insights" className="mt-4">
            <div className="space-y-4">
              {/* Key Findings */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <h4 className="font-medium">Key Findings</h4>
                  <Badge variant="outline">{summarizerInsights.keyFindings.length}</Badge>
                </div>
                {summarizerInsights.keyFindings.length > 0 ? (
                  <div className="space-y-2">
                    {summarizerInsights.keyFindings.map(finding => (
                      <div key={finding.id} className="flex items-start gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                        <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm">{finding.text}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {finding.confidence}% confidence
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No key findings identified yet.</p>
                )}
              </div>

              {/* Contradictions */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-orange-600" />
                  <h4 className="font-medium">Contradictions</h4>
                  <Badge variant="outline">{summarizerInsights.contradictions.length}</Badge>
                </div>
                {summarizerInsights.contradictions.length > 0 ? (
                  <div className="space-y-2">
                    {summarizerInsights.contradictions.map(contradiction => (
                      <div key={contradiction.id} className="flex items-start gap-2 p-2 bg-orange-50 dark:bg-orange-950/20 rounded">
                        <AlertTriangle className="w-3 h-3 text-orange-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm">{contradiction.text}</p>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {contradiction.severity} severity
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No contradictions detected.</p>
                )}
              </div>

              {/* Promising Leads */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Target className="w-4 h-4 text-blue-600" />
                  <h4 className="font-medium">Promising Leads</h4>
                  <Badge variant="outline">{summarizerInsights.promisingLeads.length}</Badge>
                </div>
                {summarizerInsights.promisingLeads.length > 0 ? (
                  <div className="space-y-2">
                    {summarizerInsights.promisingLeads.map(lead => (
                      <div key={lead.id} className="flex items-start gap-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded">
                        <Target className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm">{lead.text}</p>
                          <Badge variant="secondary" className="text-xs mt-1">
                            Priority: {lead.priority}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No promising leads identified yet.</p>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="analysis" className="mt-4">
            <div className="space-y-4">
              {/* Theme Tags */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Tags className="w-4 h-4 text-indigo-600" />
                  <h4 className="font-medium">Theme Tags</h4>
                  <Badge variant="outline">{summarizerInsights.themeTags.length}</Badge>
                </div>
                {summarizerInsights.themeTags.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {summarizerInsights.themeTags.map(tag => (
                      <Badge key={tag.id} variant="secondary" className="text-xs">
                        {tag.name} ({tag.count})
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No themes identified yet.</p>
                )}
              </div>

              {/* Variables */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="w-4 h-4 text-purple-600" />
                  <h4 className="font-medium">Variables</h4>
                  <Badge variant="outline">{summarizerInsights.variables.length}</Badge>
                </div>
                {summarizerInsights.variables.length > 0 ? (
                  <div className="space-y-2">
                    {summarizerInsights.variables.map(variable => (
                      <div key={variable.id} className="p-2 bg-purple-50 dark:bg-purple-950/20 rounded">
                        <div className="font-medium text-sm">{variable.name}</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">{variable.description}</div>
                        <div className="flex gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            Impact: {variable.impact}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {variable.confidence}% confidence
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No variables identified yet.</p>
                )}
              </div>

              {/* Open Questions */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <HelpCircle className="w-4 h-4 text-gray-600" />
                  <h4 className="font-medium">Open Questions</h4>
                  <Badge variant="outline">{summarizerInsights.openQuestions.length}</Badge>
                </div>
                {summarizerInsights.openQuestions.length > 0 ? (
                  <div className="space-y-2">
                    {summarizerInsights.openQuestions.map(question => (
                      <div key={question.id} className="flex items-start gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                        <HelpCircle className="w-3 h-3 text-gray-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm">{question.text}</p>
                          <div className="flex gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {question.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {question.priority} priority
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No open questions identified yet.</p>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default SummarizerPanel;