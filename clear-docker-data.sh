#!/bin/bash

# Clear Mock Data from Docker Environment
# This script will completely clear all mock/demo data from your Docker containers

echo "🧹 SynergyAI Docker Data Cleanup"
echo "================================="
echo ""

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Error: Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to stop containers
stop_containers() {
    echo "🛑 Stopping SynergyAI containers..."
    docker-compose down
    echo "✅ Containers stopped"
    echo ""
}

# Function to clear database volume
clear_database() {
    echo "🗄️ Clearing database volume..."
    
    # Remove the postgres data volume
    if docker volume ls | grep -q "postgres_data"; then
        docker volume rm thoughtsync_postgres_data 2>/dev/null || \
        docker volume rm $(docker volume ls -q | grep postgres_data) 2>/dev/null || \
        echo "⚠️ Could not remove postgres volume (may not exist)"
    fi
    
    # Remove redis data volume
    if docker volume ls | grep -q "redis_data"; then
        docker volume rm thoughtsync_redis_data 2>/dev/null || \
        docker volume rm $(docker volume ls -q | grep redis_data) 2>/dev/null || \
        echo "⚠️ Could not remove redis volume (may not exist)"
    fi
    
    echo "✅ Database volumes cleared"
    echo ""
}

# Function to rebuild containers
rebuild_containers() {
    echo "🔨 Rebuilding containers without cache..."
    
    # Build with no cache to ensure fresh containers
    docker-compose build --no-cache
    
    echo "✅ Containers rebuilt"
    echo ""
}

# Function to start containers
start_containers() {
    echo "🚀 Starting fresh containers..."
    
    # Start containers
    docker-compose up -d
    
    echo "✅ Containers started"
    echo ""
}

# Function to clear database data via API
clear_database_data() {
    echo "🧹 Clearing database data..."
    
    # Wait for backend to be ready
    echo "⏳ Waiting for backend to be ready..."
    sleep 10
    
    # Try to clear demo data via the backend container
    docker exec synergyai-backend npm run db:clear-demo 2>/dev/null || \
    echo "⚠️ Could not clear database data via API (database may be empty)"
    
    echo "✅ Database data clearing attempted"
    echo ""
}

# Function to show container status
show_status() {
    echo "📊 Container Status:"
    docker-compose ps
    echo ""
    
    echo "🌐 Application URLs:"
    echo "Frontend: http://localhost:3000"
    echo "Backend:  http://localhost:3001"
    echo "Database: localhost:5432"
    echo ""
}

# Main execution
main() {
    check_docker
    
    echo "This will:"
    echo "1. Stop all SynergyAI containers"
    echo "2. Remove database volumes (all data will be lost)"
    echo "3. Rebuild containers from scratch"
    echo "4. Start fresh containers"
    echo ""
    
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        stop_containers
        clear_database
        rebuild_containers
        start_containers
        clear_database_data
        show_status
        
        echo "🎉 Mock data cleanup completed!"
        echo ""
        echo "📋 Next steps:"
        echo "1. Open http://localhost:3000 in your browser"
        echo "2. Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)"
        echo "3. You should see an empty workspace list"
        echo ""
        echo "💡 If mock data still appears:"
        echo "- Clear browser storage (F12 > Application > Storage > Clear All)"
        echo "- Try incognito/private browsing mode"
        echo "- Check browser console for errors"
    else
        echo "❌ Operation cancelled"
        exit 0
    fi
}

# Run the main function
main
