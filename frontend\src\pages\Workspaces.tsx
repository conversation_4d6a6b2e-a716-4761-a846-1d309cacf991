
import React, { useState } from 'react';
import { Plus, Folder, Calendar, ArrowRight, Target, Trash } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useNavigate } from 'react-router-dom';
import useAppStore from '../stores/useAppStore';

interface Workspace {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  conversationCount: number;
}

const Workspaces: React.FC = () => {
  const navigate = useNavigate();
  const { createWorkspaceConversation } = useAppStore();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [workspaceName, setWorkspaceName] = useState('');
  const [workspaceDescription, setWorkspaceDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Empty workspaces - will be populated from backend
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);

  const handleCreateWorkspace = async () => {
    if (!workspaceName.trim() || !workspaceDescription.trim()) return;
    
    setIsCreating(true);
    
    // Create new workspace
    const newWorkspace: Workspace = {
      id: Date.now().toString(),
      name: workspaceName.trim(),
      description: workspaceDescription.trim(),
      createdAt: new Date(),
      conversationCount: 0
    };
    
    setWorkspaces(prev => [newWorkspace, ...prev]);
    
    // Create initial conversation and navigate to chat
    createWorkspaceConversation(workspaceName.trim(), workspaceDescription.trim());
    
    // Reset form
    setWorkspaceName('');
    setWorkspaceDescription('');
    setIsCreateDialogOpen(false);
    setIsCreating(false);
    
    // Navigate to chat page
    navigate('/chat');
  };

  const handleOpenWorkspace = (workspace: Workspace) => {
    // In a real app, you'd load the workspace data here
    createWorkspaceConversation(workspace.name, workspace.description);
    navigate('/chat');
  };

  const handleDeleteWorkspace = (workspaceId: string) => {
    setWorkspaces(prev => prev.filter(workspace => workspace.id !== workspaceId));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card border-b border-border px-8 py-8 shadow-soft">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-semibold text-foreground mb-3">SynergyAI</h1>
          <p className="text-muted-foreground text-lg font-normal">
            Multi-agent AI workspace for accelerated problem-solving
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-8 py-12">
        {/* Welcome Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-semibold mb-4">Welcome back!</h2>
          <p className="text-muted-foreground text-lg">
            Create a new workspace or continue working on an existing one.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Create New Workspace */}
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Card className="cursor-pointer hover:shadow-soft-lg transition-all duration-300 border-2 border-dashed border-muted hover:border-primary/30 animate-fade-in">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <div className="w-20 h-20 bg-primary/10 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300 hover:scale-110">
                    <Plus className="w-10 h-10 text-primary" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-3">Create New Workspace</h3>
                  <p className="text-muted-foreground text-center text-lg leading-relaxed">
                    Start a new project with AI-powered problem solving
                  </p>
                </CardContent>
              </Card>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle className="text-2xl">Create New Workspace</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 pt-4">
                <div className="space-y-2">
                  <label htmlFor="workspace-name" className="text-base font-medium">
                    Workspace Name
                  </label>
                  <Input
                    id="workspace-name"
                    placeholder="e.g., AI Research Project, Business Analysis"
                    value={workspaceName}
                    onChange={(e) => setWorkspaceName(e.target.value)}
                    className="text-base py-3"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="workspace-description" className="text-base font-medium">
                    Goal & Description
                  </label>
                  <Textarea
                    id="workspace-description"
                    placeholder="Describe what you want to accomplish in this workspace..."
                    value={workspaceDescription}
                    onChange={(e) => setWorkspaceDescription(e.target.value)}
                    rows={4}
                    className="text-base"
                  />
                </div>
                <Button 
                  onClick={handleCreateWorkspace}
                  disabled={!workspaceName.trim() || !workspaceDescription.trim() || isCreating}
                  className="w-full py-3 text-base"
                >
                  {isCreating ? 'Creating...' : 'Create Workspace'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* Continue Previous Work */}
          <Card className="cursor-pointer hover:shadow-soft-lg transition-all duration-300 animate-fade-in" onClick={() => navigate('/chat')}>
            <CardContent className="flex flex-col items-center justify-center py-16">
              <div className="w-20 h-20 bg-secondary/50 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300 hover:scale-110">
                <ArrowRight className="w-10 h-10 text-secondary-foreground" />
              </div>
              <h3 className="text-2xl font-semibold mb-3">Continue Current Session</h3>
              <p className="text-muted-foreground text-center text-lg leading-relaxed">
                Resume your ongoing conversations and analysis
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Workspaces */}
        <div>
          <h3 className="text-2xl font-semibold mb-8">Recent Workspaces</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workspaces.map((workspace) => (
              <Card 
                key={workspace.id} 
                className="cursor-pointer hover:shadow-soft-lg transition-all duration-300 animate-fade-in group"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3 flex-1" onClick={() => handleOpenWorkspace(workspace)}>
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Folder className="w-5 h-5 text-primary" />
                      </div>
                      <CardTitle className="text-xl font-semibold">{workspace.name}</CardTitle>
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive/10 hover:text-destructive"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Trash className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Workspace</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{workspace.name}"? This action cannot be undone and will permanently remove all conversations and data associated with this workspace.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction 
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            onClick={() => handleDeleteWorkspace(workspace.id)}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  <CardDescription className="text-base leading-relaxed mt-3" onClick={() => handleOpenWorkspace(workspace)}>
                    {workspace.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0" onClick={() => handleOpenWorkspace(workspace)}>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{workspace.createdAt.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      <span>{workspace.conversationCount} conversations</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Workspaces;
