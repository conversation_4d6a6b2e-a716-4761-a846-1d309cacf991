#!/bin/bash

# ThoughtSync Production Setup Script
set -e

echo "🔧 Setting up ThoughtSync for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Create necessary directories
create_directories() {
    log_step "Creating directory structure..."
    
    mkdir -p deployment/nginx/ssl
    mkdir -p deployment/monitoring/grafana/dashboards
    mkdir -p deployment/monitoring/grafana/datasources
    mkdir -p backups
    mkdir -p logs
    
    log_info "Directories created ✅"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    log_step "Generating SSL certificates..."
    
    if [ ! -f "deployment/nginx/ssl/cert.pem" ]; then
        openssl req -x509 -newkey rsa:4096 -keyout deployment/nginx/ssl/key.pem \
            -out deployment/nginx/ssl/cert.pem -days 365 -nodes \
            -subj "/C=US/ST=State/L=City/O=ThoughtSync/CN=thoughtsync.app"
        
        log_info "SSL certificates generated ✅"
        log_warn "Using self-signed certificates. Replace with proper SSL certificates for production."
    else
        log_info "SSL certificates already exist ✅"
    fi
}

# Create environment file template
create_env_template() {
    log_step "Creating production environment template..."
    
    if [ ! -f ".env.production" ]; then
        cat > .env.production << 'EOF'
# Production Environment Configuration

# Database
POSTGRES_PASSWORD=your-secure-postgres-password
DATABASE_URL=********************************************************************/thoughtsync_db

# Redis
REDIS_PASSWORD=your-secure-redis-password

# JWT
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters
JWT_EXPIRES_IN=7d

# Server
NODE_ENV=production
PORT=3001

# Frontend
FRONTEND_URL=https://thoughtsync.app

# AI APIs
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=thoughtsync-context

# Monitoring
SENTRY_DSN=your-sentry-dsn
GRAFANA_PASSWORD=your-grafana-admin-password

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF
        
        log_info "Environment template created: .env.production ✅"
        log_warn "Please update .env.production with your actual values before deployment!"
    else
        log_info "Environment file already exists ✅"
    fi
}

# Create Grafana configuration
create_grafana_config() {
    log_step "Creating Grafana configuration..."
    
    # Datasource configuration
    cat > deployment/monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    # Dashboard configuration
    cat > deployment/monitoring/grafana/dashboards/dashboard.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    log_info "Grafana configuration created ✅"
}

# Set proper permissions
set_permissions() {
    log_step "Setting file permissions..."
    
    chmod +x scripts/deploy.sh
    chmod +x scripts/setup-production.sh
    chmod 600 .env.production 2>/dev/null || true
    chmod 600 deployment/nginx/ssl/key.pem 2>/dev/null || true
    
    log_info "Permissions set ✅"
}

# Validate Docker setup
validate_docker() {
    log_step "Validating Docker setup..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Test Docker
    if docker info &> /dev/null; then
        log_info "Docker is running ✅"
    else
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Create backup script
create_backup_script() {
    log_step "Creating backup script..."
    
    cat > scripts/backup.sh << 'EOF'
#!/bin/bash

# ThoughtSync Backup Script
set -e

BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "Creating backup in $BACKUP_DIR..."

# Database backup
docker-compose -f deployment/docker-compose.prod.yml exec -T postgres \
    pg_dump -U thoughtsync thoughtsync_db > "$BACKUP_DIR/database.sql"

# Redis backup
docker-compose -f deployment/docker-compose.prod.yml exec -T redis \
    redis-cli --rdb - > "$BACKUP_DIR/redis.rdb"

# Configuration backup
cp -r deployment "$BACKUP_DIR/"
cp .env.production "$BACKUP_DIR/" 2>/dev/null || true

echo "Backup completed: $BACKUP_DIR"
EOF

    chmod +x scripts/backup.sh
    log_info "Backup script created ✅"
}

# Main setup function
main() {
    log_info "Starting ThoughtSync production setup..."
    
    validate_docker
    create_directories
    generate_ssl_certificates
    create_env_template
    create_grafana_config
    create_backup_script
    set_permissions
    
    log_info "🎉 Production setup completed!"
    echo ""
    log_info "Next steps:"
    echo "  1. Update .env.production with your actual configuration values"
    echo "  2. Replace self-signed SSL certificates with proper ones for production"
    echo "  3. Run './scripts/deploy.sh' to deploy the application"
    echo "  4. Access the application at https://thoughtsync.app"
    echo "  5. Monitor the application at https://thoughtsync.app:3000 (Grafana)"
}

# Run main function
main "$@"