
import React, { useRef, useEffect } from 'react';
import { Loader2, Building, Zap, MessageSquare } from 'lucide-react';
import ChatMessage from './ChatMessage';
import { Message } from '../stores/useAppStore';

interface MessageListProps {
  messages: (Message & { mode: 'core' | 'plan' | 'general' })[];
  activeMode: 'core' | 'plan' | 'general';
  isLoading: boolean;
  editingMessageId: string | null;
  editingContent: string;
  setEditingContent: (content: string) => void;
  onTogglePin: (messageId: string) => void;
  onCopyMessage: () => void;
  onRefreshMessage: (messageId: string) => void;
  onDeleteMessage: (messageId: string) => void;
  onEditMessage: (messageId: string, content: string) => void;
  onResendMessage: (messageId: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  activeMode,
  isLoading,
  editingMessageId,
  editingContent,
  setEditingContent,
  onTogglePin,
  onCopyMessage,
  onRefreshMessage,
  onDeleteMessage,
  onEditMessage,
  onResendMessage,
  onSaveEdit,
  onCancelEdit,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center px-4 sm:px-8">
          <div className="text-4xl sm:text-6xl mb-4 sm:mb-6">🤖</div>
          <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3">Start a conversation</h3>
          <p className="text-sm sm:text-base text-muted-foreground mb-3 sm:mb-4">
            {activeMode === 'plan' 
              ? 'Plan AI will help you strategize and create prompts for Core AI'
              : 'Ask a question or describe your problem'
            }
          </p>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {activeMode === 'plan' 
              ? 'Plan AI has access to Core conversation context for better planning'
              : 'Use the toggle above to switch between Core, Plan, and General AI modes'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {messages.map((message) => (
        <div key={message.id} className="space-y-2 sm:space-y-3">
          {message.role === 'assistant' && (
            <div className="flex items-center gap-2 sm:gap-3 text-xs sm:text-sm text-muted-foreground">
              {message.mode === 'core' ? (
                <>
                  <div className="w-5 h-5 sm:w-6 sm:h-6 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Building className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-primary" />
                  </div>
                  <span className="font-medium">Core AI</span>
                </>
              ) : message.mode === 'plan' ? (
                <>
                  <div className="w-5 h-5 sm:w-6 sm:h-6 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Zap className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-primary" />
                  </div>
                  <span className="font-medium">Plan AI</span>
                </>
              ) : (
                 <>
                  <div className="w-5 h-5 sm:w-6 sm:h-6 bg-primary/10 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-primary" />
                  </div>
                  <span className="font-medium">General AI</span>
                </>
              )}
            </div>
          )}

          {/* Edit mode for user messages */}
          {editingMessageId === message.id ? (
            <div className="flex justify-end">
              <div className="max-w-[80%] bg-primary rounded-lg p-4">
                <textarea
                  value={editingContent}
                  onChange={(e) => setEditingContent(e.target.value)}
                  className="w-full bg-primary-foreground text-primary border-0 rounded p-2 resize-none focus:outline-none focus:ring-2 focus:ring-ring"
                  rows={3}
                  autoFocus
                />
                <div className="flex gap-2 mt-2">
                  <button
                    onClick={onSaveEdit}
                    className="px-3 py-1 bg-primary-foreground text-primary rounded text-sm hover:bg-primary-foreground/90"
                  >
                    Save
                  </button>
                  <button
                    onClick={onCancelEdit}
                    className="px-3 py-1 bg-primary-foreground/20 text-primary-foreground rounded text-sm hover:bg-primary-foreground/30"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <ChatMessage
              message={message}
              onPin={() => {
                if (message.mode === 'core' || message.mode === 'plan') {
                  onTogglePin(message.id)
                }
              }}
              onCopy={onCopyMessage}
              onRefresh={() => onRefreshMessage(message.id)}
              onDelete={() => onDeleteMessage(message.id)}
              onEdit={() => onEditMessage(message.id, message.content)}
              onResend={() => onResendMessage(message.id)}
            />
          )}
        </div>
      ))}
      {isLoading && (
        <div className="flex justify-start">
          <div className="bg-muted rounded-2xl px-4 sm:px-6 py-3 sm:py-4 flex items-center gap-2 sm:gap-3 shadow-soft">
            <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin text-primary" />
            <span className="text-muted-foreground font-medium text-sm sm:text-base">
              {activeMode === 'core' ? 'Core' : activeMode === 'plan' ? 'Plan' : 'General'} is thinking...
            </span>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
