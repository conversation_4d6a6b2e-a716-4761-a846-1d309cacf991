
import React from 'react';
import { Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '../hooks/use-toast';

interface ConversationStub {
  id: string;
  name: string;
  parentId?: string | null;
  isContextShielded: boolean;
  createdAt: Date;
  messages: {
    core: Array<{ id: string; role: 'user' | 'assistant'; content: string; timestamp: Date; isPinned?: boolean }>;
    plan: Array<{ id: string; role: 'user' | 'assistant'; content: string; timestamp: Date; isPinned?: boolean }>;
  };
}

interface ChatActionsProps {
  activeMode: 'core' | 'plan' | 'general';
  activeConversation: ConversationStub | null;
}

const ChatActions: React.FC<ChatActionsProps> = ({
  activeMode,
  activeConversation,
}) => {
  const { toast } = useToast();

  const downloadCoreConversation = () => {
    if (activeMode !== 'core' || !activeConversation) {
      toast({
        title: "Cannot download",
        description: "Only Core conversations can be downloaded",
        variant: "destructive"
      });
      return;
    }

    const coreMessages = activeConversation.messages.core;
    
    if (coreMessages.length === 0) {
      toast({
        title: "No messages",
        description: "There are no messages to download",
        variant: "destructive"
      });
      return;
    }

    // Create markdown content
    const markdownContent = `# Core AI Conversation

**Conversation:** ${activeConversation.name}
**Date:** ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
**Context Shield:** ${activeConversation.isContextShielded ? 'Enabled' : 'Disabled'}

---

${coreMessages.map(msg => {
  const role = msg.role === 'user' ? 'You' : 'Core AI';
  const content = msg.content.replace(/\n/g, '\n\n');
  return `## ${role}\n\n${content}`;
}).join('\n\n---\n\n')}

---
*Downloaded from SynergyAI on ${new Date().toLocaleDateString()}*
`;

    // Create blob and download
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `core-conversation-${activeConversation.name.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Conversation Saved",
      description: "Your Core conversation has been downloaded as a markdown file.",
    });
  };

  if (activeMode === 'core' && activeConversation && activeConversation.messages.core.length > 0) {
    return (
      <Button
        onClick={downloadCoreConversation}
        variant="outline"
        size="sm"
        className="p-2"
        title="Download Core conversation as Markdown"
      >
        <Download className="w-4 h-4" />
      </Button>
    );
  }

  return null;
};

export default ChatActions;
