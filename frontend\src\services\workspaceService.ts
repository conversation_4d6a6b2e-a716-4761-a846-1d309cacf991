import { API_BASE_URL, API_ENDPOINTS } from '../config/api';

export interface Workspace {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  settings: Record<string, any>;
  owner?: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
  };
  _count?: {
    projects: number;
    members: number;
  };
  userRole?: string;
}

export interface CreateWorkspaceRequest {
  name: string;
  description?: string;
  settings?: Record<string, any>;
}

export interface UpdateWorkspaceRequest {
  name?: string;
  description?: string;
  settings?: Record<string, any>;
}

class WorkspaceService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  async getWorkspaces(): Promise<{ workspaces: Workspace[] }> {
    try {
      return this.request<{ workspaces: Workspace[] }>(API_ENDPOINTS.WORKSPACES.LIST);
    } catch (error) {
      // If backend is not available, return empty workspaces
      console.warn('Backend not available, returning empty workspaces:', error);
      return { workspaces: [] };
    }
  }

  async getWorkspace(id: string): Promise<{ workspace: Workspace }> {
    return this.request<{ workspace: Workspace }>(API_ENDPOINTS.WORKSPACES.GET(id));
  }

  async createWorkspace(data: CreateWorkspaceRequest): Promise<{ workspace: Workspace; message: string }> {
    try {
      return this.request<{ workspace: Workspace; message: string }>(
        API_ENDPOINTS.WORKSPACES.CREATE,
        {
          method: 'POST',
          body: JSON.stringify(data),
        }
      );
    } catch (error) {
      // If backend is not available, create a local workspace
      console.warn('Backend not available, creating local workspace:', error);
      const localWorkspace: Workspace = {
        id: `local-${Date.now()}`,
        name: data.name,
        description: data.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ownerId: 'local-user',
        settings: data.settings || {},
        _count: { projects: 0, members: 1 },
        userRole: 'owner'
      };
      return {
        workspace: localWorkspace,
        message: 'Workspace created locally (offline mode)'
      };
    }
  }

  async updateWorkspace(
    id: string,
    data: UpdateWorkspaceRequest
  ): Promise<{ workspace: Workspace; message: string }> {
    return this.request<{ workspace: Workspace; message: string }>(
      API_ENDPOINTS.WORKSPACES.UPDATE(id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      }
    );
  }

  async deleteWorkspace(id: string): Promise<{ message: string }> {
    try {
      return this.request<{ message: string }>(
        API_ENDPOINTS.WORKSPACES.DELETE(id),
        {
          method: 'DELETE',
        }
      );
    } catch (error) {
      // If backend is not available, just return success for local workspaces
      console.warn('Backend not available, deleting local workspace:', error);
      if (id.startsWith('local-')) {
        return { message: 'Local workspace deleted' };
      }
      throw error; // Re-throw for non-local workspaces
    }
  }

  // Helper method to transform backend workspace to frontend format
  transformWorkspace(backendWorkspace: Workspace): Workspace & { conversationCount: number } {
    return {
      ...backendWorkspace,
      createdAt: backendWorkspace.createdAt,
      conversationCount: backendWorkspace._count?.projects || 0,
    };
  }
}

export const workspaceService = new WorkspaceService();
