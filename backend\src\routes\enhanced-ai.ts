import { Router } from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { validateBody, validateParams } from '../middleware/validation';
import { createError } from '../middleware/errorHandler';
import { aiOrchestrator } from '../lib/aiOrchestrator';
import { vectorStore } from '../lib/vectorStore';
import SocketManager from '../lib/socket';
import { z } from 'zod';

const router = Router();

// All enhanced AI routes require authentication
router.use(authenticateToken);

// Enhanced AI execution schema
const enhancedExecuteSchema = z.object({
  conversationId: z.string().uuid('Invalid conversation ID'),
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']),
  prompt: z.string().min(1, 'Prompt is required'),
  model: z.string().optional(),
  contextBundles: z.array(z.string().uuid()).default([]),
  goalShielded: z.boolean().default(true),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(8192).optional(),
  streaming: z.boolean().default(false),
});

const contextSearchSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  projectId: z.string().uuid('Invalid project ID'),
  limit: z.number().min(1).max(20).default(10),
  minScore: z.number().min(0).max(1).default(0.7),
  type: z.enum(['conversation', 'insight', 'goal', 'external', 'project-summary']).optional(),
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']).optional(),
});

const addContextSchema = z.object({
  projectId: z.string().uuid('Invalid project ID'),
  content: z.string().min(1, 'Content is required'),
  type: z.enum(['conversation', 'insight', 'goal', 'external', 'project-summary']),
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']).optional(),
  tags: z.array(z.string()).default([]),
});

type EnhancedExecuteInput = z.infer<typeof enhancedExecuteSchema>;
type ContextSearchInput = z.infer<typeof contextSearchSchema>;
type AddContextInput = z.infer<typeof addContextSchema>;

// Helper function to check conversation access
const checkConversationAccess = async (conversationId: string, userId: string) => {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    include: {
      project: {
        include: {
          workspace: {
            include: {
              members: {
                where: { userId },
                select: { role: true }
              },
              owner: {
                select: { id: true }
              }
            }
          }
        }
      }
    }
  });

  if (!conversation) {
    throw createError('Conversation not found', 404);
  }

  const isOwner = conversation.project.workspace.owner.id === userId;
  const member = conversation.project.workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  return conversation;
};

// Enhanced AI execution with full orchestration
router.post('/execute-enhanced', validateBody(enhancedExecuteSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const {
      conversationId,
      agentType,
      prompt,
      model,
      contextBundles,
      goalShielded,
      temperature,
      maxTokens,
      streaming
    }: EnhancedExecuteInput = req.body;

    // Verify conversation access
    const conversation = await checkConversationAccess(conversationId, req.user!.id);

    // Create execution task
    const task = await prisma.executionTask.create({
      data: {
        conversationId,
        model: model || 'auto-select',
        status: 'queued',
        progress: 0,
        cost: 0,
        createdBy: req.user!.id,
      }
    });

    // Get socket manager for real-time updates
    const socketManager = req.app.get('socketManager') as SocketManager;

    // Notify clients that AI task started
    socketManager.emitToConversation(conversationId, 'ai:task-started', {
      taskId: task.id,
      agentType,
      startedBy: req.user,
      timestamp: new Date()
    });

    // Execute AI prompt with orchestration (async)
    setImmediate(async () => {
      try {
        // Update task status to processing
        await prisma.executionTask.update({
          where: { id: task.id },
          data: {
            status: 'processing',
            startTime: new Date(),
            progress: 10
          }
        });

        socketManager.emitToConversation(conversationId, 'ai:task-progress', {
          taskId: task.id,
          progress: 10,
          status: 'processing'
        });

        // Execute with AI orchestrator
        const executionContext = {
          projectId: conversation.projectId,
          conversationId,
          agentType,
          userId: req.user!.id,
          goalShielded,
          contextBundles,
          maxTokens,
          temperature,
        };

        const aiResponse = await aiOrchestrator.executePrompt(prompt, executionContext, model);

        // Update task progress
        await prisma.executionTask.update({
          where: { id: task.id },
          data: { progress: 80 }
        });

        socketManager.emitToConversation(conversationId, 'ai:task-progress', {
          taskId: task.id,
          progress: 80,
          status: 'finalizing'
        });

        // Create assistant message with the response
        const message = await prisma.message.create({
          data: {
            conversationId,
            agentType,
            role: 'assistant',
            content: aiResponse.content,
            metadata: {
              taskId: task.id,
              model: aiResponse.model,
              tokens: aiResponse.tokens,
              cost: aiResponse.cost,
              goalShielded,
              contextBundles,
              processingTime: aiResponse.metadata.processingTime,
              provider: aiResponse.metadata.provider,
            },
            createdBy: req.user!.id,
          },
          include: {
            creator: {
              select: { id: true, username: true, firstName: true, lastName: true }
            }
          }
        });

        // Update task as completed
        await prisma.executionTask.update({
          where: { id: task.id },
          data: {
            status: 'completed',
            progress: 100,
            cost: aiResponse.cost,
            endTime: new Date(),
            result: {
              messageId: message.id,
              content: aiResponse.content,
              tokens: aiResponse.tokens,
              model: aiResponse.model,
            }
          }
        });

        // Update conversation timestamp
        await prisma.conversation.update({
          where: { id: conversationId },
          data: { updatedAt: new Date() }
        });

        // Notify clients of completion
        socketManager.emitToConversation(conversationId, 'ai:task-completed', {
          taskId: task.id,
          message,
          aiResponse: {
            tokens: aiResponse.tokens,
            cost: aiResponse.cost,
            model: aiResponse.model,
            processingTime: aiResponse.metadata.processingTime,
          },
          timestamp: new Date()
        });

        // Emit message sent event
        socketManager.emitToConversation(conversationId, 'message:sent', {
          message,
          sentBy: req.user,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Error in enhanced AI execution:', error);
        
        // Update task as failed
        await prisma.executionTask.update({
          where: { id: task.id },
          data: {
            status: 'failed',
            endTime: new Date(),
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }
        });

        // Notify clients of failure
        socketManager.emitToConversation(conversationId, 'ai:task-failed', {
          taskId: task.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date()
        });
      }
    });

    res.status(201).json({
      message: 'Enhanced AI execution started',
      task: {
        id: task.id,
        status: task.status,
        progress: task.progress,
        createdAt: task.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

// Search context using vector store
router.post('/context/search', validateBody(contextSearchSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const {
      query,
      projectId,
      limit,
      minScore,
      type,
      agentType
    }: ContextSearchInput = req.body;

    // Verify project access
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        workspace: {
          include: {
            members: {
              where: { userId: req.user!.id },
              select: { role: true }
            },
            owner: {
              select: { id: true }
            }
          }
        }
      }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    const isOwner = project.workspace.owner.id === req.user!.id;
    const member = project.workspace.members[0];
    
    if (!isOwner && !member) {
      throw createError('Access denied', 403);
    }

    // Search using vector store
    const results = await vectorStore.searchSimilarContext(query, projectId, {
      limit,
      minScore,
      type,
      agentType,
    });

    res.json({
      query,
      results,
      count: results.length,
      vectorStoreAvailable: vectorStore.isAvailable()
    });
  } catch (error) {
    next(error);
  }
});

// Add context to vector store
router.post('/context/add', validateBody(addContextSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const {
      projectId,
      content,
      type,
      agentType,
      tags
    }: AddContextInput = req.body;

    // Verify project access (same as above)
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        workspace: {
          include: {
            members: {
              where: { userId: req.user!.id },
              select: { role: true }
            },
            owner: {
              select: { id: true }
            }
          }
        }
      }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    const isOwner = project.workspace.owner.id === req.user!.id;
    const member = project.workspace.members[0];
    
    if (!isOwner && !member) {
      throw createError('Access denied', 403);
    }

    // Add to vector store
    const contextId = `manual-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    await vectorStore.addContext({
      id: contextId,
      content,
      metadata: {
        type,
        projectId,
        userId: req.user!.id,
        timestamp: new Date(),
        agentType,
        tags,
      }
    });

    res.status(201).json({
      message: 'Context added successfully',
      contextId,
      vectorStoreAvailable: vectorStore.isAvailable()
    });
  } catch (error) {
    next(error);
  }
});

// Get AI orchestrator status and available models
router.get('/status', async (req: AuthenticatedRequest, res, next) => {
  try {
    const health = await aiOrchestrator.healthCheck();
    const providers = aiOrchestrator.getAvailableProviders();
    const models = aiOrchestrator.getAvailableModels();
    const vectorStoreHealth = await vectorStore.healthCheck();

    res.json({
      aiOrchestrator: health,
      vectorStore: vectorStoreHealth,
      availableProviders: providers,
      availableModels: models,
      capabilities: {
        enhancedExecution: true,
        contextSearch: vectorStore.isAvailable(),
        goalShielding: true,
        multiModelSupport: models.length > 1,
        realTimeUpdates: true,
      }
    });
  } catch (error) {
    next(error);
  }
});

// Estimate cost for a prompt
router.post('/estimate-cost', validateBody(z.object({
  prompt: z.string().min(1),
  model: z.string().min(1),
})), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { prompt, model } = req.body;
    
    const estimatedCost = await aiOrchestrator.estimateCost(prompt, model);
    
    res.json({
      prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      model,
      estimatedCost,
      currency: 'USD'
    });
  } catch (error) {
    next(error);
  }
});

// Get context statistics for a project
router.get('/context/stats/:projectId', validateParams(z.object({
  projectId: z.string().uuid()
})), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { projectId } = req.params;

    // Verify project access (same pattern as above)
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        workspace: {
          include: {
            members: {
              where: { userId: req.user!.id },
              select: { role: true }
            },
            owner: {
              select: { id: true }
            }
          }
        }
      }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    const isOwner = project.workspace.owner.id === req.user!.id;
    const member = project.workspace.members[0];
    
    if (!isOwner && !member) {
      throw createError('Access denied', 403);
    }

    const stats = await vectorStore.getContextStats(projectId);

    res.json({
      projectId,
      contextStats: stats,
      vectorStoreAvailable: vectorStore.isAvailable()
    });
  } catch (error) {
    next(error);
  }
});

export { router as enhancedAiRoutes };