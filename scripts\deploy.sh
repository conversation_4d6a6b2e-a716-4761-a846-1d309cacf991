#!/bin/bash

# ThoughtSync Production Deployment Script
set -e

echo "🚀 Starting ThoughtSync deployment..."

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_ENABLED=${2:-true}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    if [ ! -f ".env.production" ]; then
        log_error ".env.production file not found"
        exit 1
    fi
    
    log_info "Prerequisites check passed ✅"
}

# Backup database
backup_database() {
    if [ "$BACKUP_ENABLED" = "true" ]; then
        log_info "Creating database backup..."
        
        BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        docker-compose exec -T postgres pg_dump -U thoughtsync thoughtsync_db > "$BACKUP_DIR/database.sql"
        
        if [ $? -eq 0 ]; then
            log_info "Database backup created: $BACKUP_DIR/database.sql ✅"
        else
            log_error "Database backup failed"
            exit 1
        fi
    else
        log_warn "Database backup skipped"
    fi
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Pull latest images
    log_info "Pulling latest images..."
    docker-compose -f deployment/docker-compose.prod.yml pull
    
    # Build and start services
    log_info "Building and starting services..."
    docker-compose -f deployment/docker-compose.prod.yml up -d --build
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check health
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_info "Application is healthy ✅"
    else
        log_error "Application health check failed"
        exit 1
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    docker-compose -f deployment/docker-compose.prod.yml exec backend npm run db:migrate
    
    if [ $? -eq 0 ]; then
        log_info "Database migrations completed ✅"
    else
        log_error "Database migrations failed"
        exit 1
    fi
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    docker image prune -f
    log_info "Cleanup completed ✅"
}

# Main deployment process
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    
    check_prerequisites
    backup_database
    deploy_application
    run_migrations
    cleanup
    
    log_info "🎉 Deployment completed successfully!"
    log_info "Application is available at: https://thoughtsync.app"
    log_info "Monitoring dashboard: https://thoughtsync.app:3000"
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"