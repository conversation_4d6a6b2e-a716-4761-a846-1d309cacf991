// Migration utilities for converting existing data to new agent structure

import { AppState, Goal, Milestone, ProjectSummary, InsightExtraction } from './types';

export interface LegacyAppState {
  // Old agent structure
  isLoading: {
    core: boolean;
    plan: boolean;
    compass: boolean;
    general: boolean;
  };
  modelSettings: {
    core: any;
    plan: any;
    compass: any;
    general: any;
  };
  compassSummary: string;
  compassChecklist: Array<{ id: string; task: string; isComplete: boolean }>;
}

export function migrateAgentState(legacyState: Partial<LegacyAppState>): Partial<AppState> {
  const migrated: Partial<AppState> = {};

  // Migrate loading states
  if (legacyState.isLoading) {
    migrated.isLoading = {
      projectNavigator: legacyState.isLoading.core || false,
      promptEngineering: legacyState.isLoading.plan || false,
      advancedExecution: false, // New agent
      summarizer: legacyState.isLoading.compass || false,
      iterativeLoop: false, // New agent
      general: legacyState.isLoading.general || false,
    };
  }

  // Migrate model settings
  if (legacyState.modelSettings) {
    migrated.modelSettings = {
      projectNavigator: legacyState.modelSettings.core || { model: 'gpt-4o', temperature: 0.7, maxOutputTokens: 2048 },
      promptEngineering: legacyState.modelSettings.plan || { model: 'claude-3-haiku-20240307', temperature: 0.8, maxOutputTokens: 2048 },
      advancedExecution: {
        model: 'gpt-4o',
        temperature: 0.7,
        maxOutputTokens: 2048,
        contextAware: true,
        goalShielded: true,
        memoryRich: true,
        allowExternalModels: true
      },
      summarizer: legacyState.modelSettings.compass || { model: 'gemini-1.5-flash', temperature: 0.5, maxOutputTokens: 4096, thinkingBudget: 8192 },
      iterativeLoop: {
        automaticIteration: true,
        humanApprovalPoints: true,
        convergenceCriteria: ['all-project-goals-met', 'solution-milestone-reached', 'user-pause-request'],
        maxIterations: 10
      },
      general: legacyState.modelSettings.general || { model: 'gpt-4o-mini', temperature: 0.7, maxOutputTokens: 2048 },
    };
  }

  // Migrate compass data to summarizer
  if (legacyState.compassSummary) {
    migrated.condensedSummary = legacyState.compassSummary;
  }

  if (legacyState.compassChecklist) {
    // Convert checklist items to goals
    migrated.projectGoals = legacyState.compassChecklist.map((item, index) => ({
      id: item.id || `migrated-goal-${index}`,
      text: item.task,
      type: 'short-term' as const,
      completed: item.isComplete,
      priority: 'medium' as const,
      createdAt: new Date()
    }));

    // Create basic insight extraction from checklist
    migrated.summarizerInsights = {
      keyFindings: [],
      contradictions: [],
      promisingLeads: [],
      themeTags: [],
      variables: [],
      openQuestions: legacyState.compassChecklist
        .filter(item => !item.isComplete)
        .map((item, index) => ({
          id: `question-${index}`,
          text: `How to complete: ${item.task}?`,
          category: 'task-completion',
          priority: 'medium' as const
        }))
    };
  }

  // Initialize new workflow.md specific state
  migrated.currentProject = null;
  migrated.projectMilestones = [];
  migrated.promptStrategies = [];
  migrated.contextBundles = [];
  migrated.executionQueue = [];
  migrated.iterationHistory = [];
  migrated.goalShieldingEnabled = true; // Default to shielded

  return migrated;
}

export function createDefaultProject(): ProjectSummary | null {
  return null;
}

export function createDefaultInsightExtraction(): InsightExtraction {
  return {
    keyFindings: [],
    contradictions: [],
    promisingLeads: [],
    themeTags: [],
    variables: [],
    openQuestions: []
  };
}

// Helper to check if state needs migration
export function needsMigration(state: any): boolean {
  return (
    state.isLoading?.core !== undefined ||
    state.modelSettings?.core !== undefined ||
    state.compassSummary !== undefined ||
    state.compassChecklist !== undefined
  );
}