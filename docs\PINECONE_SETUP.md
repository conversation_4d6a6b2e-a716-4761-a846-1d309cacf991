# 🌲 Pinecone Setup Guide for SynergyAI

This guide will help you set up Pinecone vector database for SynergyAI's AI context management.

## 🎯 What You Need

SynergyAI requires these Pinecone configuration values:
- `PINECONE_API_KEY` - Your API key for authentication
- `PINECONE_ENVIRONMENT` - The cloud region where your index is hosted
- `PINECONE_INDEX_NAME` - The name of your vector index (we'll use "thoughtsync-context")

## 📋 Step-by-Step Setup

### 1. Create Pinecone Account
1. Go to [Pinecone.io](https://www.pinecone.io/)
2. Click "Sign Up" and create a free account
3. Verify your email address

### 2. Find Your Environment
After logging in to your Pinecone dashboard:

**Method 1: Dashboard Header**
- Look at the top-right corner of the dashboard
- You'll see your environment displayed (e.g., "us-east-1-aws")

**Method 2: Project Settings**
- Click on your project name in the top-left
- Go to "Settings" or "Project Settings"
- Find the "Environment" field

**Method 3: API Keys Page**
- Click "API Keys" in the left sidebar
- Your environment is displayed next to your API key

### 3. Get Your API Key
1. In the Pinecone dashboard, click "API Keys" in the left sidebar
2. You'll see your API key listed
3. Click "Copy" to copy the full API key
4. It will look like: `********-1234-1234-1234-********9abc`

### 4. Create Your Index
1. Click "Indexes" in the left sidebar
2. Click "Create Index" button
3. Fill in the details:
   ```
   Index Name: thoughtsync-context
   Dimension: 1536
   Metric: cosine
   Environment: [Select the same environment from step 2]
   ```
4. Click "Create Index"
5. Wait for the index to be created (usually takes 1-2 minutes)

## 🔧 Common Environments

Here are the most common Pinecone environments:

| Environment | Region | Cloud Provider |
|-------------|--------|----------------|
| `us-east-1-aws` | US East (Virginia) | AWS |
| `us-west1-gcp` | US West (Oregon) | Google Cloud |
| `eu-west1-aws` | Europe (Ireland) | AWS |
| `asia-southeast1-gcp` | Asia Southeast (Singapore) | Google Cloud |

**Most free tier accounts use `us-east-1-aws`**

## 📝 Configuration Example

Once you have your values, add them to your `.env` file:

```bash
# Pinecone Configuration
PINECONE_API_KEY="********-1234-1234-1234-********9abc"
PINECONE_ENVIRONMENT="us-east-1-aws"
PINECONE_INDEX_NAME="thoughtsync-context"
```

## ✅ Verify Your Setup

You can verify your Pinecone setup by:

1. **Check the index exists:**
   - Go to "Indexes" in your Pinecone dashboard
   - Confirm "thoughtsync-context" is listed and "Ready"

2. **Test the connection:**
   - Start your SynergyAI backend
   - Check the logs for any Pinecone connection errors
   - Visit `http://localhost:3001/health/detailed` to see vector store status

## 🚨 Troubleshooting

### "Environment not found" error
- Double-check your `PINECONE_ENVIRONMENT` value
- Ensure it matches exactly what's shown in your dashboard
- Common mistake: using region names instead of environment names

### "Index not found" error
- Verify your index name is exactly `thoughtsync-context`
- Check that the index is in "Ready" status in the dashboard
- Ensure the index is in the same environment as your API key

### "Authentication failed" error
- Verify your API key is copied correctly
- Check for extra spaces or characters
- Regenerate your API key if needed

### "Dimension mismatch" error
- Ensure your index dimension is set to `1536`
- This matches OpenAI's embedding dimension
- You may need to delete and recreate the index with correct dimensions

## 💡 Tips

1. **Free Tier Limits:**
   - Pinecone free tier includes 1 index
   - 5M vectors storage
   - This is sufficient for development and small deployments

2. **Index Management:**
   - You can view index statistics in the dashboard
   - Monitor usage to stay within free tier limits
   - Consider upgrading for production use

3. **Security:**
   - Keep your API key secure and never commit it to version control
   - Rotate your API key periodically
   - Use different indexes for development and production

## 📞 Need Help?

If you're still having trouble:
1. Check the [Pinecone Documentation](https://docs.pinecone.io/)
2. Verify your account is activated and verified
3. Contact Pinecone support if you suspect an account issue
4. Check SynergyAI logs for specific error messages