
import React from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { GitBranch } from 'lucide-react';
import useAppStore from '@/stores/useAppStore';

const BranchModal: React.FC = () => {
  const { isBranchModalOpen, branchTitle, toggleBranchModal, setBranchTitle, createBranchedConversation } = useAppStore();

  const handleCreateBranch = () => {
    if (branchTitle.trim()) {
      createBranchedConversation(branchTitle.trim());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreateBranch();
    }
  };

  return (
    <Dialog open={isBranchModalOpen} onOpenChange={toggleBranchModal}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="w-5 h-5 text-primary" />
            Branch Conversation
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="branch-title">Branch Title</Label>
            <Input
              id="branch-title"
              placeholder="Enter a title for your new branch..."
              value={branchTitle}
              onChange={(e) => setBranchTitle(e.target.value)}
              onKeyDown={handleKeyDown}
              autoFocus
            />
          </div>
          <div className="text-sm text-muted-foreground">
            This will create a new conversation with all current Core messages copied over, 
            allowing you to explore different directions while maintaining context.
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={toggleBranchModal}>
              Cancel
            </Button>
            <Button onClick={handleCreateBranch} disabled={!branchTitle.trim()}>
              Create Branch
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BranchModal;
