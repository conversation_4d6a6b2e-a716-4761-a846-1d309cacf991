import { Router } from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { validateBody, validateParams, validateQuery } from '../middleware/validation';
import { createError } from '../middleware/errorHandler';
import {
  createConversationSchema,
  updateConversationSchema,
  createMessageSchema,
  updateMessageSchema,
  branchConversationSchema,
  conversationParamsSchema,
  messageParamsSchema,
  projectConversationParamsSchema,
  messagesQuerySchema,
  CreateConversationInput,
  UpdateConversationInput,
  CreateMessageInput,
  UpdateMessageInput,
  BranchConversationInput,
  MessagesQuery
} from '../schemas/conversation';

const router = Router();

// All conversation routes require authentication
router.use(authenticateToken);

// Helper function to check conversation access
const checkConversationAccess = async (conversationId: string, userId: string) => {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    include: {
      project: {
        include: {
          workspace: {
            include: {
              members: {
                where: { userId },
                select: { role: true }
              },
              owner: {
                select: { id: true }
              }
            }
          }
        }
      }
    }
  });

  if (!conversation) {
    throw createError('Conversation not found', 404);
  }

  const isOwner = conversation.project.workspace.owner.id === userId;
  const member = conversation.project.workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  return conversation;
};

// Helper function to check project access for conversation creation
const checkProjectAccess = async (projectId: string, userId: string) => {
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    include: {
      workspace: {
        include: {
          members: {
            where: { userId },
            select: { role: true }
          },
          owner: {
            select: { id: true }
          }
        }
      }
    }
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  const isOwner = project.workspace.owner.id === userId;
  const member = project.workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  return project;
};

// Get conversations in project
router.get('/project/:projectId', validateParams(projectConversationParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { projectId } = req.params;
    
    await checkProjectAccess(projectId, req.user!.id);

    const conversations = await prisma.conversation.findMany({
      where: { projectId },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: { 
            messages: true,
            branches: true
          }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    res.json({ conversations });
  } catch (error) {
    next(error);
  }
});

// Create conversation
router.post('/project/:projectId', validateParams(projectConversationParamsSchema), validateBody(createConversationSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { projectId } = req.params;
    const { name, parentId, isContextShielded }: CreateConversationInput = req.body;

    await checkProjectAccess(projectId, req.user!.id);

    // If parentId is provided, verify it exists and belongs to the same project
    if (parentId) {
      const parentConversation = await prisma.conversation.findUnique({
        where: { id: parentId },
        select: { projectId: true }
      });

      if (!parentConversation || parentConversation.projectId !== projectId) {
        throw createError('Invalid parent conversation', 400);
      }
    }

    const conversation = await prisma.conversation.create({
      data: {
        projectId,
        name,
        parentId,
        isContextShielded,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        project: {
          select: { id: true, title: true }
        },
        _count: {
          select: { 
            messages: true,
            branches: true
          }
        }
      }
    });

    res.status(201).json({
      message: 'Conversation created successfully',
      conversation
    });
  } catch (error) {
    next(error);
  }
});

// Get conversation details
router.get('/:id', validateParams(conversationParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkConversationAccess(id, req.user!.id);

    const conversation = await prisma.conversation.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        branches: {
          select: {
            id: true,
            name: true,
            createdAt: true,
            _count: {
              select: { messages: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        project: {
          select: { id: true, title: true }
        },
        _count: {
          select: { messages: true }
        }
      }
    });

    res.json({ conversation });
  } catch (error) {
    next(error);
  }
});

// Update conversation
router.put('/:id', validateParams(conversationParamsSchema), validateBody(updateConversationSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData: UpdateConversationInput = req.body;

    await checkConversationAccess(id, req.user!.id);

    const conversation = await prisma.conversation.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: { 
            messages: true,
            branches: true
          }
        }
      }
    });

    res.json({
      message: 'Conversation updated successfully',
      conversation
    });
  } catch (error) {
    next(error);
  }
});

// Delete conversation
router.delete('/:id', validateParams(conversationParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkConversationAccess(id, req.user!.id);

    await prisma.conversation.delete({
      where: { id }
    });

    res.json({ message: 'Conversation deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Branch conversation
router.post('/:id/branch', validateParams(conversationParamsSchema), validateBody(branchConversationSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { name }: BranchConversationInput = req.body;

    const parentConversation = await checkConversationAccess(id, req.user!.id);

    // Create new conversation as a branch
    const branchConversation = await prisma.conversation.create({
      data: {
        projectId: parentConversation.projectId,
        name,
        parentId: id,
        isContextShielded: parentConversation.isContextShielded,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        parent: {
          select: { id: true, name: true }
        },
        _count: {
          select: { messages: true }
        }
      }
    });

    // Copy messages from parent conversation (project-navigator messages only for branching)
    const parentMessages = await prisma.message.findMany({
      where: {
        conversationId: id,
        agentType: 'project-navigator'
      },
      orderBy: { createdAt: 'asc' }
    });

    if (parentMessages.length > 0) {
      const messagesToCopy = parentMessages.map(msg => ({
        conversationId: branchConversation.id,
        agentType: msg.agentType,
        role: msg.role,
        content: msg.content,
        metadata: msg.metadata,
        isPinned: msg.isPinned,
        createdBy: msg.createdBy,
        createdAt: msg.createdAt,
      }));

      await prisma.message.createMany({
        data: messagesToCopy
      });
    }

    res.status(201).json({
      message: 'Conversation branched successfully',
      conversation: branchConversation
    });
  } catch (error) {
    next(error);
  }
});

// Messages endpoints

// Get conversation messages
router.get('/:id/messages', validateParams(conversationParamsSchema), validateQuery(messagesQuerySchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { agentType, limit, offset }: MessagesQuery = req.query as any;
    
    await checkConversationAccess(id, req.user!.id);

    const messages = await prisma.message.findMany({
      where: {
        conversationId: id,
        ...(agentType && { agentType })
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      },
      orderBy: { createdAt: 'asc' },
      take: limit,
      skip: offset
    });

    const totalCount = await prisma.message.count({
      where: {
        conversationId: id,
        ...(agentType && { agentType })
      }
    });

    res.json({ 
      messages,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    });
  } catch (error) {
    next(error);
  }
});

// Send message
router.post('/:id/messages', validateParams(conversationParamsSchema), validateBody(createMessageSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { agentType, role, content, metadata }: CreateMessageInput = req.body;

    await checkConversationAccess(id, req.user!.id);

    const message = await prisma.message.create({
      data: {
        conversationId: id,
        agentType,
        role,
        content,
        metadata,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    // Update conversation's updatedAt
    await prisma.conversation.update({
      where: { id },
      data: { updatedAt: new Date() }
    });

    res.status(201).json({
      message: 'Message sent successfully',
      data: message
    });
  } catch (error) {
    next(error);
  }
});

// Update message
router.put('/messages/:id', validateParams(messageParamsSchema), validateBody(updateMessageSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData: UpdateMessageInput = req.body;

    // Check if message exists and user has access
    const message = await prisma.message.findUnique({
      where: { id },
      include: { conversation: true }
    });

    if (!message) {
      throw createError('Message not found', 404);
    }

    await checkConversationAccess(message.conversationId, req.user!.id);

    const updatedMessage = await prisma.message.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.json({
      message: 'Message updated successfully',
      data: updatedMessage
    });
  } catch (error) {
    next(error);
  }
});

// Delete message
router.delete('/messages/:id', validateParams(messageParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const message = await prisma.message.findUnique({
      where: { id },
      include: { conversation: true }
    });

    if (!message) {
      throw createError('Message not found', 404);
    }

    await checkConversationAccess(message.conversationId, req.user!.id);

    await prisma.message.delete({
      where: { id }
    });

    res.json({ message: 'Message deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Toggle message pin
router.post('/messages/:id/pin', validateParams(messageParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const message = await prisma.message.findUnique({
      where: { id },
      include: { conversation: true }
    });

    if (!message) {
      throw createError('Message not found', 404);
    }

    await checkConversationAccess(message.conversationId, req.user!.id);

    const updatedMessage = await prisma.message.update({
      where: { id },
      data: { isPinned: !message.isPinned },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.json({
      message: `Message ${updatedMessage.isPinned ? 'pinned' : 'unpinned'} successfully`,
      data: updatedMessage
    });
  } catch (error) {
    next(error);
  }
});

export { router as conversationRoutes };