
import { create } from 'zustand';
import { AppState } from './types';
import { createDefaultConversation, createConversationActions, ConversationActions } from './conversationActions';
import { createMessageActions, MessageActions } from './messageActions';
import { createUIActions, UIActions } from './uiActions';
import { createAgentActions, AgentActions } from './agentActions';
import { migrateAgentState, needsMigration, createDefaultProject, createDefaultInsightExtraction } from './migration';

// Re-export types for backward compatibility and new workflow.md types
export type { 
  Message, 
  ChecklistItem, 
  Conversation, 
  ModelConfig,
  Goal,
  Milestone,
  ProjectSummary,
  PromptStrategy,
  ContextBundle,
  ExecutionTask,
  IterationCycle,
  InsightExtraction,
  ExecutionConfig,
  LoopConfig
} from './types';

type AppActions = ConversationActions & MessageActions & UIActions & AgentActions;

const useAppStore = create<AppState & AppActions>((set, get) => {
  // Create conversation actions first
  const conversationActions = createConversationActions(set, get);
  
  // Create message actions with conversation actions dependency
  const messageActions = createMessageActions(set, get, conversationActions);
  
  // Create UI actions with message actions dependency
  const uiActions = createUIActions(set, get, messageActions);
  
  // Create agent actions for workflow.md agents
  const agentActions = createAgentActions(set, get);

  return {
    // Initial state - empty for backend communication
    conversations: [],
    activeConversationId: '',
    generalChatMessages: [],
    
    // New workflow.md agent state
    summarizerInsights: createDefaultInsightExtraction(),
    condensedSummary: '',
    
    // Updated agent loading states
    isLoading: {
      projectNavigator: false,
      promptEngineering: false,
      advancedExecution: false,
      summarizer: false,
      iterativeLoop: false,
      general: false,
    },
    
    apiKeys: {
      openai: '',
      anthropic: '',
      google: '',
      openrouter: '',
    },
    
    // Updated model settings for workflow.md agents
    modelSettings: {
      projectNavigator: { model: 'gpt-4o', temperature: 0.7, maxOutputTokens: 2048 },
      promptEngineering: { model: 'claude-3-haiku-20240307', temperature: 0.8, maxOutputTokens: 2048 },
      advancedExecution: { 
        model: 'gpt-4o', 
        temperature: 0.7, 
        maxOutputTokens: 2048,
        contextAware: true,
        goalShielded: true,
        memoryRich: true,
        allowExternalModels: true
      },
      summarizer: { model: 'gemini-1.5-flash', temperature: 0.5, maxOutputTokens: 4096, thinkingBudget: 8192 },
      iterativeLoop: {
        automaticIteration: true,
        humanApprovalPoints: true,
        convergenceCriteria: ['all-project-goals-met', 'solution-milestone-reached', 'user-pause-request'],
        maxIterations: 10
      },
      general: { model: 'gpt-4o-mini', temperature: 0.7, maxOutputTokens: 2048 },
    },
    
    // New workflow.md specific state - empty for backend communication
    currentProject: null,
    projectGoals: [],
    projectMilestones: [],
    promptStrategies: [],
    contextBundles: [],
    executionQueue: [],
    iterationHistory: [],
    goalShieldingEnabled: true,
    
    // Existing UI state
    isSettingsModalOpen: false,
    isBranchModalOpen: false,
    branchTitle: '',
    currentWorkspace: null,
    draftPrompt: '',

    // Actions
    ...conversationActions,
    ...messageActions,
    ...uiActions,
    ...agentActions,
  };
});

// No initial conversation setup - will be created via backend

export default useAppStore;
