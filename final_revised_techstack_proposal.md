# 🛠️ **Final Revised Tech Stack for ThoughtSyncApp**
*Mapping workflow.md agents to tandem-cognition template*

This revision precisely maps the 7-stage ThoughtSyncApp workflow to the existing tandem-cognition architecture, replacing the current Core/Plan/Compass agents with the specific AI agents defined in workflow.md.

---

## 🎯 **Agent Mapping: workflow.md → tandem-cognition**

### **Current tandem-cognition Agents** ❌ *Replace*
```typescript
// Current agents to be replaced:
- Core AI: Systematic problem-solving assistant
- Plan AI: Creative catalyst for ideation  
- Compass AI: Progress tracking and analysis
- General AI: Versatile conversations
```

### **New ThoughtSyncApp Agents** ✅ *Implement from workflow.md*
```typescript
// Map directly to workflow.md stages:
1. Project Navigator AI     → Replace Core AI
2. Prompt/Context Engineering AI → Replace Plan AI  
3. Advanced AI Execution    → New execution layer
4. Summarizer AI           → Replace Compass AI
5. Iterative Intelligence Loop → System orchestration
6. Internal Advanced AI    → Context-aware processing
7. Human-in-the-Loop       → Governance controls
```

---

## 🔄 **Detailed Agent Architecture**

### **1. Project Navigator AI** 🧭 *Replaces Core AI*
```typescript
interface ProjectNavigatorAgent {
  role: 'project-navigator';
  capabilities: [
    'problem-exploration',
    'goal-clarification', 
    'objective-definition',
    'project-summary-generation',
    'milestone-tracking',
    'scope-constraint-management'
  ];
  
  // Replace Core AI model config
  modelConfig: {
    model: 'gpt-4o', // Keep existing model
    temperature: 0.7,
    systemPrompt: `You are the Project Navigator AI. Your role is to:
    - Help users explore ideas and clarify problems
    - Generate and update Project Summaries with goals, scope, constraints
    - Define milestones and success criteria
    - Provide real-time updates to project plans as insights emerge
    - Always be available for project plan refinement`
  };
}
```

### **2. Prompt/Context Engineering AI** 🧠 *Replaces Plan AI*
```typescript
interface PromptEngineeringAgent {
  role: 'prompt-context-engineering';
  capabilities: [
    'prompt-strategy-creation',
    'context-curation',
    'query-structure-design',
    'dependency-mapping',
    'model-selection',
    'context-injection',
    'prompt-optimization'
  ];
  
  // Replace Plan AI model config
  modelConfig: {
    model: 'claude-3-haiku-20240307', // Keep existing model
    temperature: 0.8,
    systemPrompt: `You are the Prompt/Context Engineering AI. Your role is to:
    - Translate project goals into executable prompts
    - Build prompt strategies with query structure and context dependencies
    - Select appropriate AI models (internal or external)
    - Inject condensed summaries from previous outputs
    - Allow users to revise strategies and prompt-context bundles
    - Optimize prompts for maximum effectiveness`
  };
}
```

### **3. Advanced AI Execution** ⚙️ *New Execution Layer*
```typescript
interface AdvancedAIExecution {
  role: 'advanced-ai-execution';
  capabilities: [
    'prompt-execution',
    'model-orchestration',
    'internal-external-routing',
    'response-processing',
    'context-awareness',
    'goal-shielding'
  ];
  
  // New execution system
  executionConfig: {
    internalModel: {
      model: 'gpt-4o',
      contextAware: true,
      goalShielded: true, // Ultimate goal hidden by default
      memoryRich: true
    },
    externalModel: {
      allowMarkdownImport: true,
      textOutputImport: true
    },
    sharedKnowledgeSystem: true
  };
}
```

### **4. Summarizer AI** ✍️ *Replaces Compass AI*
```typescript
interface SummarizerAgent {
  role: 'summarizer-insight-extraction';
  capabilities: [
    'output-distillation',
    'condensed-summary-generation',
    'full-detail-preservation',
    'theme-tagging',
    'variable-extraction',
    'open-question-identification',
    'insight-categorization'
  ];
  
  // Replace Compass AI model config
  modelConfig: {
    model: 'gemini-1.5-flash', // Keep existing model
    temperature: 0.5,
    thinkingBudget: 8192,
    systemPrompt: `You are the Summarizer AI. Your role is to:
    - Distill AI outputs into condensed summaries for quick review
    - Preserve full-detail outputs available on request
    - Apply tagging system to capture themes, variables, open questions
    - Extract actionable insights and patterns
    - Identify contradictions and promising leads
    - Generate structured summaries for context injection`
  };
}
```

### **5. Iterative Intelligence Loop** ♻️ *System Orchestration*
```typescript
interface IterativeIntelligenceLoop {
  role: 'iterative-intelligence-orchestration';
  capabilities: [
    'insight-analysis',
    'goal-updates',
    'pivot-suggestions',
    'gap-identification',
    'contradiction-highlighting',
    'lead-prioritization',
    'context-reconstruction',
    'continuity-management'
  ];
  
  // System-level orchestration
  loopConfig: {
    projectNavigatorIntegration: true,
    promptEngineeringFeedback: true,
    automaticIteration: true,
    humanApprovalPoints: true,
    convergenceCriteria: [
      'all-project-goals-met',
      'solution-milestone-reached',
      'invention-breakthrough',
      'user-pause-request'
    ]
  };
}
```

---

## 🏗️ **Updated Zustand Store Architecture**

### **Replace Current Agent State** 🔄
```typescript
// Current state structure to modify:
interface AppState {
  // REMOVE: Current agent structure
  // isLoading: { core, plan, compass, general }
  // modelSettings: { core, plan, compass, general }
  
  // REPLACE WITH: ThoughtSync agent structure
  isLoading: {
    projectNavigator: boolean;
    promptEngineering: boolean;
    advancedExecution: boolean;
    summarizer: boolean;
    iterativeLoop: boolean;
  };
  
  modelSettings: {
    projectNavigator: ModelConfig;
    promptEngineering: ModelConfig;
    advancedExecution: ExecutionConfig;
    summarizer: ModelConfig;
    iterativeLoop: LoopConfig;
  };
  
  // NEW: ThoughtSync specific state
  currentProject: {
    id: string;
    summary: string;
    goals: Goal[];
    milestones: Milestone[];
    constraints: Constraint[];
    assumptions: string[];
  };
  
  promptStrategies: PromptStrategy[];
  contextDependencies: ContextMap[];
  executionQueue: ExecutionTask[];
  iterationHistory: IterationCycle[];
  
  // NEW: Goal shielding control
  goalShieldingEnabled: boolean;
  
  // KEEP: Existing conversation management
  conversations: Conversation[];
  activeConversationId: string;
  // ... rest of existing state
}
```

### **Updated Agent Actions** 🔄
```typescript
// Replace current agent actions
interface ThoughtSyncActions {
  // Project Navigator actions (replace Core AI actions)
  updateProjectSummary: (summary: ProjectSummary) => void;
  addGoal: (goal: Goal) => void;
  updateMilestone: (milestone: Milestone) => void;
  refineProjectScope: (scope: ProjectScope) => void;
  
  // Prompt Engineering actions (replace Plan AI actions)
  createPromptStrategy: (strategy: PromptStrategy) => void;
  updateContextDependencies: (context: ContextMap) => void;
  selectTargetModel: (model: ModelSelection) => void;
  injectContext: (context: ContextBundle) => void;
  
  // Advanced Execution actions (new)
  executePromptSequence: (prompts: PromptSequence) => void;
  toggleGoalShielding: () => void;
  routeToExternalModel: (config: ExternalModelConfig) => void;
  
  // Summarizer actions (replace Compass AI actions)
  generateSummary: (content: string) => void;
  extractInsights: (outputs: AIOutput[]) => void;
  tagThemes: (content: string) => void;
  identifyOpenQuestions: (content: string) => void;
  
  // Iterative Loop actions (new)
  analyzeNewInsights: () => void;
  suggestPivots: () => void;
  highlightGaps: () => void;
  reconstructContext: () => void;
}
```

---

## 🎨 **Updated Package Dependencies**

### **Keep Existing Foundation** ✅
```json
{
  "dependencies": {
    // Existing packages maintained
    "react": "^18.3.1",
    "zustand": "^5.0.5",
    "openai": "^5.3.0",
    "@google/generative-ai": "^0.24.1",
    // ... all existing packages
    
    // NEW: ThoughtSync specific additions
    "langchain": "^0.1.0",          // Advanced prompt chaining
    "uuid": "^9.0.0",               // ID generation for projects/goals
    "date-fns": "^3.6.0",           // Already included ✅
    "react-markdown": "^10.1.0"     // Already included ✅
  }
}
```

---

## 🔄 **Migration Strategy**

### **Phase 1: Agent Replacement** (Week 1-2)
1. **Update Zustand Store**
   ```typescript
   // Rename existing agent states
   core → projectNavigator
   plan → promptEngineering  
   compass → summarizer
   // Add new execution and loop states
   ```

2. **Update Model Configurations**
   ```typescript
   // Keep existing model assignments but update roles
   modelSettings: {
     projectNavigator: { model: 'gpt-4o', ... },      // was core
     promptEngineering: { model: 'claude-3-haiku', ... }, // was plan
     summarizer: { model: 'gemini-1.5-flash', ... },  // was compass
     advancedExecution: { ... },                       // new
     iterativeLoop: { ... }                            // new
   }
   ```

### **Phase 2: UI Component Updates** (Week 2-3)
1. **Update UnifiedChatPanel**
   ```tsx
   // Replace agent mode tabs
   const agentModes = [
     'project-navigator',    // was 'core'
     'prompt-engineering',   // was 'plan'  
     'advanced-execution',   // new
     'summarizer'           // was 'compass' (moved to chat)
   ];
   ```

2. **Transform Compass Panel**
   ```tsx
   // Convert from Compass AI to Summarizer AI interface
   <SummarizerPanel>
     <InsightExtraction />
     <ThemeTagging />
     <OpenQuestions />
     <ActionableItems />
   </SummarizerPanel>
   ```

### **Phase 3: Advanced Features** (Week 3-4)
1. **Implement Goal Shielding**
   ```tsx
   <GoalShieldingControl>
     <Switch 
       checked={goalShieldingEnabled}
       onCheckedChange={toggleGoalShielding}
     />
     <Label>Shield ultimate goal from current prompt</Label>
   </GoalShieldingControl>
   ```

2. **Add Iterative Loop System**
   ```tsx
   <IterativeLoopPanel>
     <LoopProgress />
     <InsightAnalysis />
     <PivotSuggestions />
     <ContextReconstruction />
   </IterativeLoopPanel>
   ```

---

## 🎯 **Key Implementation Details**

### **Agent Communication Flow**
```typescript
// Workflow sequence from workflow.md
1. ProjectNavigator → generates project summary
2. PromptEngineering → creates executable prompts  
3. AdvancedExecution → executes prompts with context
4. Summarizer → distills outputs and extracts insights
5. IterativeLoop → analyzes insights, updates goals
6. [Repeat until completion criteria met]
```

### **Context Management**
```typescript
// Implement context shielding and injection
interface ContextManager {
  shieldedContext: ContextBundle[];    // Hidden from certain agents
  visibleContext: ContextBundle[];     // Available to all agents
  condensedSummaries: Summary[];       // For context injection
  dependencyMap: ContextDependency[];  // Context relationships
}
```

### **Human-in-the-Loop Controls**
```typescript
// Governance controls at every stage
interface HumanControls {
  approvePromptStrategy: boolean;
  modifyProjectGoals: boolean;
  hideInjectContext: boolean;
  chooseExecutionMethod: boolean;
  overrideAutomation: boolean;
}
```

This final revision precisely implements the workflow.md agent system within the tandem-cognition template, maintaining the excellent UI foundation while replacing the agent logic with the specific ThoughtSyncApp workflow.