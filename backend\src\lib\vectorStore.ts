import { PineconeClient } from 'pinecone-client';
import OpenAI from 'openai';
import { config } from '../config/config';

export interface ContextVector {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    type: 'conversation' | 'insight' | 'goal' | 'external' | 'project-summary';
    projectId: string;
    conversationId?: string;
    userId?: string;
    timestamp: Date;
    agentType?: string;
    relevanceScore?: number;
    tags?: string[];
  };
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: ContextVector['metadata'];
}

class VectorStore {
  private pinecone: PineconeClient | null = null;
  private openai: OpenAI | null = null;
  private indexName: string;
  private isInitialized = false;

  constructor() {
    this.indexName = config.pineconeIndexName;
    this.initializeClients();
  }

  private async initializeClients() {
    try {
      // Initialize Pinecone if API key is available
      if (config.pineconeApiKey && config.pineconeEnvironment) {
        this.pinecone = new PineconeClient({
          apiKey: config.pineconeApiKey,
          environment: config.pineconeEnvironment,
        });
        console.log('✅ Pinecone client initialized');
      }

      // Initialize OpenAI for embeddings if API key is available
      if (config.openaiApiKey) {
        this.openai = new OpenAI({
          apiKey: config.openaiApiKey,
        });
        console.log('✅ OpenAI client initialized for embeddings');
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Error initializing vector store clients:', error);
      // Continue without vector store - fallback to basic context management
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text,
        encoding_format: 'float',
      });

      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error('Failed to generate embedding');
    }
  }

  async addContext(context: Omit<ContextVector, 'embedding'>): Promise<void> {
    if (!this.isInitialized || !this.pinecone) {
      console.warn('Vector store not available, skipping context storage');
      return;
    }

    try {
      // Generate embedding for the content
      const embedding = await this.generateEmbedding(context.content);

      // Prepare vector for Pinecone
      const vector = {
        id: context.id,
        values: embedding,
        metadata: {
          content: context.content,
          type: context.metadata.type,
          projectId: context.metadata.projectId,
          conversationId: context.metadata.conversationId || '',
          userId: context.metadata.userId || '',
          timestamp: context.metadata.timestamp.toISOString(),
          agentType: context.metadata.agentType || '',
          tags: context.metadata.tags?.join(',') || '',
        },
      };

      // Upsert to Pinecone
      const index = this.pinecone.Index(this.indexName);
      await index.upsert({
        vectors: [vector],
      });

      console.log(`✅ Added context to vector store: ${context.id}`);
    } catch (error) {
      console.error('Error adding context to vector store:', error);
      // Don't throw - continue without vector storage
    }
  }

  async searchSimilarContext(
    query: string,
    projectId: string,
    options: {
      limit?: number;
      minScore?: number;
      type?: ContextVector['metadata']['type'];
      agentType?: string;
      excludeConversationId?: string;
    } = {}
  ): Promise<SearchResult[]> {
    if (!this.isInitialized || !this.pinecone) {
      console.warn('Vector store not available, returning empty results');
      return [];
    }

    try {
      const {
        limit = 10,
        minScore = 0.7,
        type,
        agentType,
        excludeConversationId,
      } = options;

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      // Build filter
      const filter: any = {
        projectId: { $eq: projectId },
      };

      if (type) {
        filter.type = { $eq: type };
      }

      if (agentType) {
        filter.agentType = { $eq: agentType };
      }

      if (excludeConversationId) {
        filter.conversationId = { $ne: excludeConversationId };
      }

      // Search in Pinecone
      const index = this.pinecone.Index(this.indexName);
      const searchResponse = await index.query({
        vector: queryEmbedding,
        topK: limit,
        filter,
        includeMetadata: true,
      });

      // Process results
      const results: SearchResult[] = [];
      
      if (searchResponse.matches) {
        for (const match of searchResponse.matches) {
          if (match.score && match.score >= minScore && match.metadata) {
            results.push({
              id: match.id,
              content: match.metadata.content as string,
              score: match.score,
              metadata: {
                type: match.metadata.type as ContextVector['metadata']['type'],
                projectId: match.metadata.projectId as string,
                conversationId: match.metadata.conversationId as string || undefined,
                userId: match.metadata.userId as string || undefined,
                timestamp: new Date(match.metadata.timestamp as string),
                agentType: match.metadata.agentType as string || undefined,
                tags: match.metadata.tags ? (match.metadata.tags as string).split(',') : undefined,
              },
            });
          }
        }
      }

      console.log(`🔍 Found ${results.length} similar contexts for query`);
      return results;
    } catch (error) {
      console.error('Error searching vector store:', error);
      return [];
    }
  }

  async updateContext(contextId: string, updates: Partial<ContextVector>): Promise<void> {
    if (!this.isInitialized || !this.pinecone) {
      console.warn('Vector store not available, skipping context update');
      return;
    }

    try {
      // For updates, we need to fetch the existing vector, update it, and upsert
      // This is a simplified implementation - in production, you might want to
      // store the original content separately and only update metadata
      
      if (updates.content) {
        // If content changed, regenerate embedding
        const embedding = await this.generateEmbedding(updates.content);
        
        const vector = {
          id: contextId,
          values: embedding,
          metadata: {
            content: updates.content,
            ...(updates.metadata && {
              type: updates.metadata.type,
              projectId: updates.metadata.projectId,
              conversationId: updates.metadata.conversationId || '',
              userId: updates.metadata.userId || '',
              timestamp: updates.metadata.timestamp?.toISOString(),
              agentType: updates.metadata.agentType || '',
              tags: updates.metadata.tags?.join(',') || '',
            }),
          },
        };

        const index = this.pinecone.Index(this.indexName);
        await index.upsert({
          vectors: [vector],
        });
      }

      console.log(`✅ Updated context in vector store: ${contextId}`);
    } catch (error) {
      console.error('Error updating context in vector store:', error);
    }
  }

  async deleteContext(contextId: string): Promise<void> {
    if (!this.isInitialized || !this.pinecone) {
      console.warn('Vector store not available, skipping context deletion');
      return;
    }

    try {
      const index = this.pinecone.Index(this.indexName);
      await index.delete1({
        ids: [contextId],
      });

      console.log(`✅ Deleted context from vector store: ${contextId}`);
    } catch (error) {
      console.error('Error deleting context from vector store:', error);
    }
  }

  async getContextStats(projectId: string): Promise<{
    totalContexts: number;
    contextsByType: Record<string, number>;
    contextsByAgent: Record<string, number>;
  }> {
    if (!this.isInitialized || !this.pinecone) {
      return {
        totalContexts: 0,
        contextsByType: {},
        contextsByAgent: {},
      };
    }

    try {
      // Note: Pinecone doesn't have a direct way to get stats
      // This would require querying and aggregating results
      // For now, return empty stats - in production, you might
      // want to maintain these stats separately
      
      return {
        totalContexts: 0,
        contextsByType: {},
        contextsByAgent: {},
      };
    } catch (error) {
      console.error('Error getting context stats:', error);
      return {
        totalContexts: 0,
        contextsByType: {},
        contextsByAgent: {},
      };
    }
  }

  // Fallback methods for when vector store is not available
  async addContextFallback(context: Omit<ContextVector, 'embedding'>): Promise<void> {
    // Store in database as fallback
    // This could be implemented using a separate table for context storage
    console.log(`📝 Storing context in database fallback: ${context.id}`);
  }

  async searchContextFallback(
    query: string,
    projectId: string,
    options: any = {}
  ): Promise<SearchResult[]> {
    // Implement basic text search using database
    // This could use PostgreSQL full-text search or simple LIKE queries
    console.log(`🔍 Using database fallback search for: ${query}`);
    return [];
  }

  // Utility methods
  isAvailable(): boolean {
    return this.isInitialized && !!this.pinecone && !!this.openai;
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unavailable';
    pinecone: boolean;
    openai: boolean;
    error?: string;
  }> {
    try {
      const pineconeAvailable = !!this.pinecone;
      const openaiAvailable = !!this.openai;

      if (pineconeAvailable && openaiAvailable) {
        return { status: 'healthy', pinecone: true, openai: true };
      } else if (pineconeAvailable || openaiAvailable) {
        return { status: 'degraded', pinecone: pineconeAvailable, openai: openaiAvailable };
      } else {
        return { status: 'unavailable', pinecone: false, openai: false };
      }
    } catch (error) {
      return {
        status: 'unavailable',
        pinecone: false,
        openai: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Singleton instance
export const vectorStore = new VectorStore();
export default VectorStore;