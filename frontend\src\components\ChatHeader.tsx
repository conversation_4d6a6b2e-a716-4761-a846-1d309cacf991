
import React from 'react';
import { GitBranch, Building, Zap, MessageSquare, Edit3, <PERSON>ader2, Shield, <PERSON>Off, Trash2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, FileText } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { AGENT_MODES, getAgentConfig, type AgentMode } from '../config/agentConfig';

// As Conversation type is not exported from the store, we define a local stub.
interface ConversationStub {
  id: string;
  name: string;
  parentId?: string | null;
  isContextShielded: boolean;
  createdAt: Date;
}

interface ChatHeaderProps {
  activeMode: 'project-navigator' | 'prompt-engineering' | 'ai-execution' | 'summarizer' | 'general';
  setActiveMode: (mode: 'project-navigator' | 'prompt-engineering' | 'ai-execution' | 'summarizer' | 'general') => void;
  activeConversation: ConversationStub | null;
  generalChatMessagesCount: number;
  coreMessagesCount: number;
  planMessagesCount: number;
  getCoreContextInfo: () => string;
  handleCreatePromptDraft: () => void;
  isGeneratingDraft: boolean;
  handleToggleContextShield: () => void;
  handleBranchConversation: () => void;
  clearGeneralChat: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  activeMode,
  setActiveMode,
  activeConversation,
  generalChatMessagesCount,
  coreMessagesCount,
  planMessagesCount,
  getCoreContextInfo,
  handleCreatePromptDraft,
  isGeneratingDraft,
  handleToggleContextShield,
  handleBranchConversation,
  clearGeneralChat,
}) => {

  const getModeDescription = () => {
    const config = getAgentConfig(activeMode as AgentMode);
    return config?.description || 'AI Agent';
  };

  return (
    <div className="bg-card px-4 sm:px-6 py-4 sm:py-5 border-b border-border">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h2 className="text-lg sm:text-xl font-semibold text-card-foreground truncate">
              {activeMode === 'general' ? 'General Chat' : activeConversation?.name}
            </h2>
            {activeConversation?.parentId && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                <GitBranch className="w-3 h-3" />
                Branch
              </div>
            )}
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {activeMode === 'general' 
              ? `${generalChatMessagesCount} messages`
              : activeMode === 'prompt-engineering' 
              ? `${planMessagesCount} strategy messages • ${getCoreContextInfo()}`
              : activeMode === 'project-navigator'
              ? `${coreMessagesCount} messages • Created ${activeConversation?.createdAt.toLocaleDateString()}`
              : activeMode === 'summarizer'
              ? `${planMessagesCount} insights • Analysis updated`
              : activeMode === 'ai-execution'
              ? `${coreMessagesCount} executions • Processing queue`
              : `${coreMessagesCount} messages`
            }
          </p>
        </div>
        <div className="flex items-center gap-1 bg-muted rounded-xl p-1 flex-shrink-0 overflow-x-auto">
          {AGENT_MODES.map((agent) => {
            const IconComponent = agent.id === 'project-navigator' ? Compass :
                               agent.id === 'prompt-engineering' ? Brain :
                               agent.id === 'ai-execution' ? Settings :
                               agent.id === 'summarizer' ? FileText :
                               MessageSquare;
            
            return (
              <button
                key={agent.id}
                onClick={() => setActiveMode(agent.id)}
                className={`flex items-center gap-2 px-2 sm:px-3 py-2 sm:py-3 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 min-w-0 whitespace-nowrap ${
                  activeMode === agent.id
                    ? 'bg-primary text-primary-foreground shadow-soft'
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                }`}
                title={agent.description}
              >
                <span className="text-sm">{agent.icon}</span>
                <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                <span className="hidden sm:inline">{agent.label}</span>
                <span className="sm:hidden">{agent.label.slice(0, 3)}</span>
              </button>
            );
          })}
        </div>
      </div>
      <div className="flex justify-between items-center">
        <p className="text-xs sm:text-sm text-muted-foreground">
          {getModeDescription()}
        </p>
        <div className="flex items-center gap-2">
          {activeMode === 'prompt-engineering' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCreatePromptDraft}
              className="flex items-center gap-1 text-xs"
              disabled={isGeneratingDraft}
            >
              {isGeneratingDraft ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Edit3 className="w-3 h-3" />
              )}
              {isGeneratingDraft ? 'Generating...' : 'Draft for Navigator'}
            </Button>
          )}

          {activeMode !== 'general' && activeConversation && (
            <div className="flex items-center gap-2">
              <Label htmlFor="context-shield" className="text-xs text-muted-foreground flex items-center gap-1">
                {activeConversation.isContextShielded ? (
                  <Shield className="w-3 h-3" />
                ) : (
                  <ShieldOff className="w-3 h-3" />
                )}
                Context Shield
              </Label>
              <Switch
                id="context-shield"
                checked={activeConversation.isContextShielded}
                onCheckedChange={handleToggleContextShield}
              />
            </div>
          )}
          
          {activeMode === 'project-navigator' && activeConversation && coreMessagesCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBranchConversation}
              className="flex items-center gap-1 text-xs"
            >
              <GitBranch className="w-3 h-3" />
              Branch
            </Button>
          )}
          
          {activeMode === 'general' && generalChatMessagesCount > 0 && (
            <button
              onClick={clearGeneralChat}
              className="flex items-center gap-1.5 text-xs text-destructive hover:text-destructive/80 transition-colors"
              title="Clear General Chat"
            >
              <Trash2 className="w-3 h-3" />
              <span>Clear</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
