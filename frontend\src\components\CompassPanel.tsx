
import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeHighlight from 'rehype-highlight';
import { RotateCw, Compass as CompassIcon, Download } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import useAppStore from '../stores/useAppStore';

const CompassPanel: React.FC = () => {
  const { compassSummary, compassChecklist, isLoading, refreshCompass } = useAppStore();
  const { toast } = useToast();
  const loading = isLoading.compass;

  const downloadMarkdown = () => {
    // Create markdown content
    const markdownContent = `# Compass Analysis & Objectives

## Summary
${compassSummary}

## Objectives Checklist

${compassChecklist.map(item => `- [${item.isComplete ? 'x' : ' '}] ${item.task}`).join('\n')}

---
*Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}*
`;

    // Create blob and download
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `compass-analysis-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Compass Analysis Saved",
      description: "Your compass summary and objectives have been downloaded as a markdown file.",
    });
  };

  return (
    <div className="flex flex-col h-full bg-card border border-border rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-card px-4 py-3 border-b border-border flex items-center justify-between">
        <div className="flex items-center gap-3">
          <CompassIcon className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-lg font-semibold text-card-foreground">Compass</h2>
            <p className="text-sm text-muted-foreground">Progress tracking & strategic guidance</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={downloadMarkdown}
            variant="outline"
            size="sm"
            className="p-2"
            title="Download as Markdown"
          >
            <Download className="w-4 h-4" />
          </Button>
          <button
            onClick={refreshCompass}
            disabled={loading}
            className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors disabled:opacity-50"
            title="Refresh analysis"
          >
            <RotateCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Summary Section */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
            📊 Conversation Summary
          </h3>
          <div className="bg-muted rounded-lg p-4 border border-border">
            {loading ? (
              <div className="flex items-center gap-2 text-muted-foreground">
                <RotateCw className="w-4 h-4 animate-spin" />
                <span>Analyzing conversations...</span>
              </div>
            ) : (
              <ReactMarkdown
                rehypePlugins={[rehypeHighlight]}
                components={{
                  h1: ({ children }) => <h1 className="text-xl font-bold mb-3 text-primary">{children}</h1>,
                  h2: ({ children }) => <h2 className="text-lg font-semibold mb-2 text-primary">{children}</h2>,
                  h3: ({ children }) => <h3 className="text-base font-medium mb-2 text-primary">{children}</h3>,
                  p: ({ children }) => <p className="mb-3 text-foreground leading-relaxed">{children}</p>,
                  ul: ({ children }) => <ul className="list-disc list-inside mb-3 space-y-1 text-foreground">{children}</ul>,
                  li: ({ children }) => <li className="text-sm">{children}</li>,
                  strong: ({ children }) => <strong className="text-foreground font-semibold">{children}</strong>,
                  code: ({ children, className }) => {
                    const isInline = !className;
                    return isInline ? (
                      <code className="bg-accent px-1 py-0.5 rounded text-primary text-sm">
                        {children}
                      </code>
                    ) : (
                      <code className="block bg-accent p-2 rounded text-sm overflow-x-auto">
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {compassSummary}
              </ReactMarkdown>
            )}
          </div>
        </div>

        {/* Checklist Section */}
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
            ✅ Objective Checklist
          </h3>
          <div className="space-y-2">
            {compassChecklist.length === 0 && !loading ? (
              <div className="bg-muted rounded-lg p-4 border border-border text-center text-muted-foreground">
                <p>Start a conversation to generate objectives</p>
              </div>
            ) : (
              compassChecklist.map((item) => (
                <div
                  key={item.id}
                  className={`flex items-center gap-3 p-3 bg-muted rounded-lg border transition-colors ${
                    item.isComplete 
                      ? 'border-green-600 bg-green-600/10' 
                      : 'border-border hover:border-accent-foreground'
                  }`}
                >
                  <Checkbox
                    checked={item.isComplete}
                    disabled
                    className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <span className={`flex-1 ${item.isComplete ? 'text-green-600 line-through' : 'text-foreground'}`}>
                    {item.task}
                  </span>
                  {item.isComplete && (
                    <span className="text-green-600 text-sm">✓</span>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompassPanel;
