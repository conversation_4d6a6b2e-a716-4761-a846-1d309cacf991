
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { ModelConfig } from '../../stores/types';

interface ModelSettingsPanelProps {
  panelId: 'core' | 'plan' | 'compass' | 'general';
  panelName: string;
  settings: ModelConfig;
  allModels: string[];
  updateModelParameter: (panel: 'core' | 'plan' | 'compass' | 'general', parameter: keyof ModelConfig, value: string | number) => void;
}

const isGeminiModel = (model: string) => {
  return model.startsWith('gemini') || model.includes('gemini');
};

const ModelSettingsPanel: React.FC<ModelSettingsPanelProps> = ({
  panelId,
  panelName,
  settings,
  allModels,
  updateModelParameter,
}) => {
  return (
    <div className="p-4 bg-gray-800/50 rounded-lg">
      <Label className="text-base font-medium text-gray-200">{panelName}</Label>
      <Select
        value={settings.model}
        onValueChange={(value) => updateModelParameter(panelId, 'model', value)}
      >
        <SelectTrigger className="bg-gray-800 border-gray-600 text-white mt-2">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          {allModels.map((model) => (
            <SelectItem key={model} value={model} className="text-white hover:bg-gray-700">
              {model}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="mt-4 space-y-4">
        <div>
          <div className="flex justify-between items-center">
            <Label htmlFor={`${panelId}-temp`} className="text-xs text-gray-400">Temperature</Label>
            <span className="text-xs font-mono text-gray-300">{settings.temperature.toFixed(1)}</span>
          </div>
          <Slider
            id={`${panelId}-temp`}
            min={0} max={1} step={0.1}
            value={[settings.temperature]}
            onValueChange={(v) => updateModelParameter(panelId, 'temperature', v[0])}
            className="mt-1 [&>span:first-child]:h-1 [&>span>span]:h-1 [&>a]:h-3.5 [&>a]:w-3.5"
          />
        </div>
        <div>
          <Label htmlFor={`${panelId}-tokens`} className="text-xs text-gray-400">Max Output Tokens</Label>
          <Input
            id={`${panelId}-tokens`} type="number" placeholder="e.g., 2048"
            value={settings.maxOutputTokens}
            onChange={(e) => updateModelParameter(panelId, 'maxOutputTokens', Number(e.target.value))}
            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1 text-sm"
            min="0"
          />
        </div>
        {isGeminiModel(settings.model) && (
          <div>
            <Label htmlFor={`${panelId}-thinking-budget`} className="text-xs text-gray-400">
              Thinking Budget
            </Label>
            <Input
              id={`${panelId}-thinking-budget`} type="number" placeholder="e.g., 8192"
              value={settings.thinkingBudget || ''}
              onChange={(e) => updateModelParameter(panelId, 'thinkingBudget', Number(e.target.value))}
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1 text-sm"
              min="0"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelSettingsPanel;
