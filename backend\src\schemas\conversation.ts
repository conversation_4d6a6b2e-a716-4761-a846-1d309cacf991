import { z } from 'zod';

export const createConversationSchema = z.object({
  name: z.string().min(1, 'Conversation name is required').max(255),
  parentId: z.string().uuid().optional(),
  isContextShielded: z.boolean().default(false),
});

export const updateConversationSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  isContextShielded: z.boolean().optional(),
});

export const createMessageSchema = z.object({
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']),
  role: z.enum(['user', 'assistant']),
  content: z.string().min(1, 'Message content is required'),
  metadata: z.record(z.any()).default({}),
});

export const updateMessageSchema = z.object({
  content: z.string().min(1).optional(),
  isPinned: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export const conversationParamsSchema = z.object({
  id: z.string().uuid('Invalid conversation ID'),
});

export const messageParamsSchema = z.object({
  id: z.string().uuid('Invalid message ID'),
});

export const projectConversationParamsSchema = z.object({
  projectId: z.string().uuid('Invalid project ID'),
});

export const branchConversationSchema = z.object({
  name: z.string().min(1, 'Branch name is required').max(255),
});

export const messagesQuerySchema = z.object({
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']).optional(),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(100)).default('50'),
  offset: z.string().transform(val => parseInt(val)).pipe(z.number().min(0)).default('0'),
});

export type CreateConversationInput = z.infer<typeof createConversationSchema>;
export type UpdateConversationInput = z.infer<typeof updateConversationSchema>;
export type CreateMessageInput = z.infer<typeof createMessageSchema>;
export type UpdateMessageInput = z.infer<typeof updateMessageSchema>;
export type BranchConversationInput = z.infer<typeof branchConversationSchema>;
export type MessagesQuery = z.infer<typeof messagesQuerySchema>;