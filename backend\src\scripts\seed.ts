import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create demo users
  const demoPassword = await bcrypt.hash('Demo123!', 12);
  
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'demo_user',
      passwordHash: demoPassword,
      firstName: 'Demo',
      lastName: 'User',
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'alice_smith',
      passwordHash: demoPassword,
      firstName: 'Alice',
      lastName: 'Smith',
    },
  });

  console.log('✅ Created demo users');

  // Create demo workspace
  const workspace = await prisma.workspace.upsert({
    where: { id: 'demo-workspace-id' },
    update: {},
    create: {
      id: 'demo-workspace-id',
      name: 'Demo Workspace',
      description: 'A demonstration workspace for SynergyAI',
      ownerId: user1.id,
      settings: {
        theme: 'light',
        notifications: true,
        aiModel: 'gpt-4'
      }
    },
  });

  // Add user2 as member
  await prisma.workspaceMember.upsert({
    where: {
      workspaceId_userId: {
        workspaceId: workspace.id,
        userId: user2.id
      }
    },
    update: {},
    create: {
      workspaceId: workspace.id,
      userId: user2.id,
      role: 'member'
    }
  });

  console.log('✅ Created demo workspace');

  // Create demo project
  const project = await prisma.project.upsert({
    where: { id: 'demo-project-id' },
    update: {},
    create: {
      id: 'demo-project-id',
      workspaceId: workspace.id,
      title: 'AI-Powered Task Management App',
      description: 'Developing a next-generation task management application with AI assistance',
      scope: [
        'User interface design',
        'AI integration',
        'Real-time collaboration',
        'Mobile application'
      ],
      assumptions: [
        'Users want AI assistance in task management',
        'Real-time collaboration is essential',
        'Mobile-first approach is preferred'
      ],
      constraints: [
        'Budget: $50,000',
        'Timeline: 6 months',
        'Team size: 5 developers'
      ],
      successCriteria: [
        '1000+ active users within 3 months',
        '4.5+ app store rating',
        'Sub-200ms response time'
      ],
      createdBy: user1.id,
    },
  });

  console.log('✅ Created demo project');

  // Create demo goals
  const goals = await Promise.all([
    prisma.goal.create({
      data: {
        projectId: project.id,
        text: 'Complete user interface designs',
        type: 'short-term',
        priority: 'high',
        completed: true,
        completedAt: new Date('2024-01-15'),
        createdBy: user1.id,
      }
    }),
    prisma.goal.create({
      data: {
        projectId: project.id,
        text: 'Implement AI task suggestion engine',
        type: 'short-term',
        priority: 'high',
        createdBy: user1.id,
      }
    }),
    prisma.goal.create({
      data: {
        projectId: project.id,
        text: 'Launch beta version to 100 users',
        type: 'long-term',
        priority: 'medium',
        createdBy: user1.id,
      }
    })
  ]);

  console.log('✅ Created demo goals');

  // Create demo milestones
  const milestones = await Promise.all([
    prisma.milestone.create({
      data: {
        projectId: project.id,
        title: 'MVP Development Complete',
        description: 'Core features implemented and tested',
        targetDate: new Date('2024-03-01'),
        createdBy: user1.id,
      }
    }),
    prisma.milestone.create({
      data: {
        projectId: project.id,
        title: 'Beta Launch',
        description: 'Application available for beta testing',
        targetDate: new Date('2024-04-15'),
        createdBy: user1.id,
      }
    }),
    prisma.milestone.create({
      data: {
        projectId: project.id,
        title: 'Public Release',
        description: 'Full public launch with marketing campaign',
        targetDate: new Date('2024-06-01'),
        createdBy: user1.id,
      }
    })
  ]);

  console.log('✅ Created demo milestones');

  // Create demo conversation
  const conversation = await prisma.conversation.create({
    data: {
      projectId: project.id,
      name: 'Project Planning Discussion',
      isContextShielded: false,
      createdBy: user1.id,
    }
  });

  // Create demo messages
  const messages = await Promise.all([
    prisma.message.create({
      data: {
        conversationId: conversation.id,
        agentType: 'project-navigator',
        role: 'user',
        content: 'I want to create a task management app that uses AI to help users prioritize and organize their work.',
        createdBy: user1.id,
      }
    }),
    prisma.message.create({
      data: {
        conversationId: conversation.id,
        agentType: 'project-navigator',
        role: 'assistant',
        content: 'That\'s an excellent project idea! Let\'s break this down systematically. An AI-powered task management app has great potential. I\'d like to help you define the scope and objectives clearly.\n\nBased on your description, I can see several key areas to explore:\n\n1. **Core Task Management Features**: Creating, organizing, and tracking tasks\n2. **AI Integration**: Intelligent prioritization, scheduling suggestions, and workflow optimization\n3. **User Experience**: Intuitive interface that makes AI assistance feel natural\n\nLet\'s start by defining your target users and their main pain points with current task management solutions. Who do you envision using this app?',
        metadata: {
          model: 'gpt-4',
          tokens: 145,
          cost: 0.0029
        },
        createdBy: user1.id,
      }
    }),
    prisma.message.create({
      data: {
        conversationId: conversation.id,
        agentType: 'project-navigator',
        role: 'user',
        content: 'The target users are knowledge workers - developers, designers, project managers, and consultants who juggle multiple projects and struggle with prioritization.',
        createdBy: user1.id,
      }
    })
  ]);

  console.log('✅ Created demo conversation and messages');

  // Create demo prompt strategies
  const promptStrategies = await Promise.all([
    prisma.promptStrategy.create({
      data: {
        workspaceId: workspace.id,
        name: 'Project Analysis Template',
        queryStructure: 'analysis',
        contextDependencies: ['project-goals', 'user-requirements'],
        targetModel: 'gpt-4',
        template: 'Analyze the following project information and provide strategic insights:\n\n{context}\n\nPlease focus on:\n1. Key opportunities and risks\n2. Resource requirements\n3. Timeline considerations\n4. Success metrics',
        isPublic: true,
        createdBy: user1.id,
      }
    }),
    prisma.promptStrategy.create({
      data: {
        workspaceId: workspace.id,
        name: 'Creative Brainstorming',
        queryStructure: 'creative',
        contextDependencies: ['project-scope', 'constraints'],
        targetModel: 'claude-3-haiku',
        template: 'Generate creative solutions for the following challenge:\n\n{context}\n\nThink outside the box and consider:\n1. Innovative approaches\n2. Unconventional solutions\n3. Emerging technologies\n4. User experience innovations',
        createdBy: user1.id,
      }
    })
  ]);

  console.log('✅ Created demo prompt strategies');

  // Create demo context bundles
  const contextBundles = await Promise.all([
    prisma.contextBundle.create({
      data: {
        workspaceId: workspace.id,
        name: 'Market Research Data',
        content: 'Recent studies show that 73% of knowledge workers feel overwhelmed by task management. The global task management software market is expected to reach $4.33 billion by 2025.',
        type: 'external',
        weight: 0.8,
        createdBy: user1.id,
      }
    }),
    prisma.contextBundle.create({
      data: {
        workspaceId: workspace.id,
        name: 'User Feedback Summary',
        content: 'Key user pain points: 1) Difficulty prioritizing tasks, 2) Context switching between tools, 3) Lack of intelligent scheduling, 4) Poor mobile experience',
        type: 'summary',
        weight: 0.9,
        createdBy: user1.id,
      }
    })
  ]);

  console.log('✅ Created demo context bundles');

  // Create demo insights
  const insights = await Promise.all([
    prisma.insight.create({
      data: {
        conversationId: conversation.id,
        type: 'key-finding',
        content: 'Target market of knowledge workers represents a significant opportunity with clear pain points',
        confidence: 85,
        metadata: { source: 'project-navigator' }
      }
    }),
    prisma.insight.create({
      data: {
        conversationId: conversation.id,
        type: 'open-question',
        content: 'What specific AI algorithms will be used for task prioritization?',
        category: 'technical',
        priority: 2,
        metadata: { urgency: 'medium' }
      }
    }),
    prisma.insight.create({
      data: {
        conversationId: conversation.id,
        type: 'promising-lead',
        content: 'Integration with calendar apps could provide powerful scheduling optimization',
        priority: 1,
        metadata: { impact: 'high' }
      }
    })
  ]);

  console.log('✅ Created demo insights');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Demo Data Summary:');
  console.log(`👥 Users: ${user1.email}, ${user2.email}`);
  console.log(`🏢 Workspace: ${workspace.name}`);
  console.log(`📁 Project: ${project.title}`);
  console.log(`🎯 Goals: ${goals.length} created`);
  console.log(`🏁 Milestones: ${milestones.length} created`);
  console.log(`💬 Messages: ${messages.length} created`);
  console.log(`🧠 Prompt Strategies: ${promptStrategies.length} created`);
  console.log(`📦 Context Bundles: ${contextBundles.length} created`);
  console.log(`💡 Insights: ${insights.length} created`);
  console.log('\n🔑 Login credentials:');
  console.log('Email: <EMAIL>');
  console.log('Password: Demo123!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Seeding failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });