// API configuration for backend communication

export const API_BASE_URL = 'http://localhost:3001';

export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',
  
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
  },
  
  // Projects
  PROJECTS: {
    LIST: '/api/projects',
    CREATE: '/api/projects',
    GET: (id: string) => `/api/projects/${id}`,
    UPDATE: (id: string) => `/api/projects/${id}`,
    DELETE: (id: string) => `/api/projects/${id}`,
  },
  
  // Conversations
  CONVERSATIONS: {
    LIST: '/api/conversations',
    CREATE: '/api/conversations',
    GET: (id: string) => `/api/conversations/${id}`,
    UPDATE: (id: string) => `/api/conversations/${id}`,
    DELETE: (id: string) => `/api/conversations/${id}`,
    MESSAGES: (id: string) => `/api/conversations/${id}/messages`,
  },
  
  // AI Services
  AI: {
    CHAT: '/api/ai/chat',
    ENHANCED: '/api/enhanced-ai',
    PROJECT_NAVIGATOR: '/api/enhanced-ai/project-navigator',
    PROMPT_ENGINEERING: '/api/enhanced-ai/prompt-engineering',
    ADVANCED_EXECUTION: '/api/enhanced-ai/advanced-execution',
    SUMMARIZER: '/api/enhanced-ai/summarizer',
  },
  
  // Workspaces
  WORKSPACES: {
    LIST: '/api/workspaces',
    CREATE: '/api/workspaces',
    GET: (id: string) => `/api/workspaces/${id}`,
    UPDATE: (id: string) => `/api/workspaces/${id}`,
    DELETE: (id: string) => `/api/workspaces/${id}`,
  },
};

// API utility functions
export const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  // Add auth token if available
  const token = localStorage.getItem('authToken');
  if (token) {
    defaultOptions.headers = {
      ...defaultOptions.headers,
      'Authorization': `Bearer ${token}`,
    };
  }
  
  const response = await fetch(url, {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  });
  
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

// Test backend connection
export const testBackendConnection = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.HEALTH}`);
    return response.ok;
  } catch (error) {
    console.error('Backend connection test failed:', error);
    return false;
  }
};