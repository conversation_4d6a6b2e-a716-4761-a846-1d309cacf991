# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist
build
.next

# Development files
.git
.gitignore
README.md
Dockerfile
.dockerignore
docker-compose.yml

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Temporary folders
tmp
temp

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache