import React, { useState, useEffect } from 'react';
import { testBackendConnection } from '../config/api';

const BackendStatus: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkConnection = async () => {
      setIsLoading(true);
      const connected = await testBackendConnection();
      setIsConnected(connected);
      setIsLoading(false);
    };

    checkConnection();
    
    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
        Checking backend...
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 text-sm">
      <div 
        className={`w-2 h-2 rounded-full ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`}
      ></div>
      <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
        Backend {isConnected ? 'Connected' : 'Disconnected'}
      </span>
      {isConnected && (
        <span className="text-gray-500">
          (http://localhost:3001)
        </span>
      )}
    </div>
  );
};

export default BackendStatus;