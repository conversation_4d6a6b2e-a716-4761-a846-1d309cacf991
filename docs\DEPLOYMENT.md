# 🚀 ThoughtSync Production Deployment Guide

This guide covers the complete deployment process for ThoughtSync in a production environment.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Docker-compatible Linux
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB, Recommended 50GB+
- **CPU**: Minimum 2 cores, Recommended 4+ cores

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL (for SSL certificates)

## 🔧 Initial Setup

### 1. Clone Repository
```bash
git clone https://github.com/your-org/thoughtsync.git
cd thoughtsync
```

### 2. Run Production Setup
```bash
chmod +x scripts/setup-production.sh
./scripts/setup-production.sh
```

### 3. Configure Environment
Edit `.env.production` with your actual values:

```bash
# Required: Update these values
POSTGRES_PASSWORD=your-secure-postgres-password
REDIS_PASSWORD=your-secure-redis-password
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
GRAFANA_PASSWORD=your-grafana-admin-password

# Optional: Update domain
FRONTEND_URL=https://your-domain.com
```

### 4. SSL Certificates
Replace self-signed certificates with proper SSL certificates:

```bash
# Copy your SSL certificates
cp your-cert.pem deployment/nginx/ssl/cert.pem
cp your-key.pem deployment/nginx/ssl/key.pem

# Set proper permissions
chmod 600 deployment/nginx/ssl/key.pem
```

## 🚀 Deployment

### Deploy Application
```bash
./scripts/deploy.sh production
```

### Verify Deployment
```bash
# Check application health
curl -f https://your-domain.com/health

# Check all services
docker-compose -f deployment/docker-compose.prod.yml ps
```

## 📊 Monitoring

### Access Monitoring Dashboard
- **Grafana**: https://your-domain.com:3000
  - Username: `admin`
  - Password: `your-grafana-admin-password`

### Health Endpoints
- **Basic Health**: `GET /health`
- **Detailed Health**: `GET /health/detailed`
- **Metrics**: `GET /metrics`
- **Readiness**: `GET /ready`
- **Liveness**: `GET /live`

### Log Monitoring
```bash
# View application logs
docker-compose -f deployment/docker-compose.prod.yml logs -f backend

# View nginx logs
docker-compose -f deployment/docker-compose.prod.yml logs -f nginx

# View all logs
docker-compose -f deployment/docker-compose.prod.yml logs -f
```

## 🔒 Security

### Firewall Configuration
```bash
# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow SSH (if needed)
sudo ufw allow 22/tcp

# Block direct database access
sudo ufw deny 5432/tcp
sudo ufw deny 6379/tcp

# Enable firewall
sudo ufw enable
```

### Security Headers
The application includes comprehensive security headers:
- HSTS (HTTP Strict Transport Security)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Referrer-Policy

### Rate Limiting
- API endpoints: 100 requests per 15 minutes per IP
- Auth endpoints: 5 requests per minute per IP
- AI endpoints: 20 requests per minute per IP

## 📦 Backup & Recovery

### Create Backup
```bash
./scripts/backup.sh
```

### Restore from Backup
```bash
# Stop services
docker-compose -f deployment/docker-compose.prod.yml down

# Restore database
docker-compose -f deployment/docker-compose.prod.yml up -d postgres
cat backups/YYYYMMDD_HHMMSS/database.sql | \
  docker-compose -f deployment/docker-compose.prod.yml exec -T postgres \
  psql -U thoughtsync thoughtsync_db

# Restore Redis
docker-compose -f deployment/docker-compose.prod.yml up -d redis
docker-compose -f deployment/docker-compose.prod.yml exec redis \
  redis-cli --rdb backups/YYYYMMDD_HHMMSS/redis.rdb

# Start all services
docker-compose -f deployment/docker-compose.prod.yml up -d
```

## 🔄 Updates & Maintenance

### Update Application
```bash
# Pull latest changes
git pull origin main

# Deploy updates
./scripts/deploy.sh production
```

### Database Migrations
```bash
# Run migrations
docker-compose -f deployment/docker-compose.prod.yml exec backend npm run db:migrate

# Generate Prisma client (if schema changed)
docker-compose -f deployment/docker-compose.prod.yml exec backend npm run db:generate
```

### Scale Services
```bash
# Scale backend instances
docker-compose -f deployment/docker-compose.prod.yml up -d --scale backend=3

# Scale with specific resource limits
docker-compose -f deployment/docker-compose.prod.yml up -d \
  --scale backend=3 \
  --memory=2g \
  --cpus=2
```

## 🐛 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose -f deployment/docker-compose.prod.yml logs service-name

# Check resource usage
docker stats

# Check disk space
df -h
```

#### Database Connection Issues
```bash
# Check database status
docker-compose -f deployment/docker-compose.prod.yml exec postgres pg_isready

# Check database logs
docker-compose -f deployment/docker-compose.prod.yml logs postgres

# Reset database connection
docker-compose -f deployment/docker-compose.prod.yml restart backend
```

#### SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in deployment/nginx/ssl/cert.pem -text -noout

# Test SSL configuration
openssl s_client -connect your-domain.com:443
```

### Performance Optimization

#### Database Optimization
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Analyze table statistics
ANALYZE;

-- Vacuum database
VACUUM ANALYZE;
```

#### Redis Optimization
```bash
# Check Redis memory usage
docker-compose -f deployment/docker-compose.prod.yml exec redis redis-cli info memory

# Monitor Redis performance
docker-compose -f deployment/docker-compose.prod.yml exec redis redis-cli monitor
```

## 📞 Support

### Health Check Commands
```bash
# Quick health check
curl -f https://your-domain.com/health

# Detailed health check
curl -s https://your-domain.com/health/detailed | jq

# Check metrics
curl -s https://your-domain.com/metrics
```

### Emergency Procedures
```bash
# Emergency stop
docker-compose -f deployment/docker-compose.prod.yml down

# Emergency restart
docker-compose -f deployment/docker-compose.prod.yml restart

# Emergency backup
./scripts/backup.sh
```

For additional support, check the logs and monitoring dashboard, or contact the development team.