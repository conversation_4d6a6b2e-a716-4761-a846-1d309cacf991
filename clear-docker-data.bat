@echo off
REM Clear Mock Data from Docker Environment
REM This script will completely clear all mock/demo data from your Docker containers

echo 🧹 SynergyAI Docker Data Cleanup
echo =================================
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo This will:
echo 1. Stop all SynergyAI containers
echo 2. Remove database volumes (all data will be lost)
echo 3. Rebuild containers from scratch
echo 4. Start fresh containers
echo.

set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ Operation cancelled
    pause
    exit /b 0
)

echo.
echo 🛑 Stopping SynergyAI containers...
docker-compose down
echo ✅ Containers stopped
echo.

echo 🗄️ Clearing database volumes...
REM Remove postgres data volume
docker volume rm thoughtsync_postgres_data 2>nul
if errorlevel 1 (
    for /f "tokens=*" %%i in ('docker volume ls -q ^| findstr postgres_data') do docker volume rm %%i 2>nul
)

REM Remove redis data volume  
docker volume rm thoughtsync_redis_data 2>nul
if errorlevel 1 (
    for /f "tokens=*" %%i in ('docker volume ls -q ^| findstr redis_data') do docker volume rm %%i 2>nul
)

echo ✅ Database volumes cleared
echo.

echo 🔨 Rebuilding containers without cache...
docker-compose build --no-cache
echo ✅ Containers rebuilt
echo.

echo 🚀 Starting fresh containers...
docker-compose up -d
echo ✅ Containers started
echo.

echo ⏳ Waiting for backend to be ready...
timeout /t 10 /nobreak >nul

echo 🧹 Clearing database data...
docker exec synergyai-backend npm run db:clear-demo 2>nul
if errorlevel 1 (
    echo ⚠️ Could not clear database data via API (database may be empty)
)
echo ✅ Database data clearing attempted
echo.

echo 📊 Container Status:
docker-compose ps
echo.

echo 🌐 Application URLs:
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:3001
echo Database: localhost:5432
echo.

echo 🎉 Mock data cleanup completed!
echo.
echo 📋 Next steps:
echo 1. Open http://localhost:3000 in your browser
echo 2. Clear your browser cache (Ctrl+F5)
echo 3. You should see an empty workspace list
echo.
echo 💡 If mock data still appears:
echo - Clear browser storage (F12 ^> Application ^> Storage ^> Clear All)
echo - Try incognito/private browsing mode
echo - Check browser console for errors

pause
