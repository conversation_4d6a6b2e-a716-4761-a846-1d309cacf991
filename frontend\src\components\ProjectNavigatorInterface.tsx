import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Edit, Target, Compass, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import useAppStore from '../stores/useAppStore';

const ProjectNavigatorInterface: React.FC = () => {
  const { 
    currentProject, 
    projectGoals, 
    projectMilestones,
    addGoal,
    addMilestone,
    updateGoal,
    updateMilestone,
    updateProjectSummary
  } = useAppStore();

  const shortTermGoals = projectGoals.filter(goal => goal.type === 'short-term');
  const longTermGoals = projectGoals.filter(goal => goal.type === 'long-term');
  const completedGoals = projectGoals.filter(goal => goal.completed).length;
  const totalGoals = projectGoals.length;
  const completedMilestones = projectMilestones.filter(milestone => milestone.completed).length;
  const totalMilestones = projectMilestones.length;

  const handleAddGoal = (type: 'short-term' | 'long-term') => {
    const goalText = prompt(`Enter ${type} goal:`);
    if (goalText?.trim()) {
      addGoal({
        text: goalText.trim(),
        type,
        completed: false,
        priority: 'medium'
      });
    }
  };

  const handleAddMilestone = () => {
    const title = prompt('Enter milestone title:');
    const description = prompt('Enter milestone description:');
    if (title?.trim()) {
      addMilestone({
        title: title.trim(),
        description: description?.trim() || '',
        targetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        completed: false
      });
    }
  };

  const handleToggleGoal = (goalId: string, completed: boolean) => {
    updateGoal(goalId, { completed });
  };

  const handleToggleMilestone = (milestoneId: string, completed: boolean) => {
    updateMilestone(milestoneId, { 
      completed,
      completedAt: completed ? new Date() : undefined
    });
  };

  return (
    <div className="space-y-4">
      {/* Project Summary Card */}
      <Card className="p-4 border-blue-200 bg-blue-50 dark:bg-blue-950/20 dark:border-blue-800">
        <div className="flex items-center gap-2 mb-3">
          <Compass className="w-6 h-6 text-blue-600" />
          <h3 className="font-semibold text-blue-900 dark:text-blue-100">Project Navigator AI</h3>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Always Available
          </Badge>
        </div>
        
        {/* Current Project Summary */}
        <div className="space-y-3">
          <div>
            <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">Current Project</Label>
            <div className="mt-1 p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-200 dark:border-blue-700">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                {currentProject?.title || 'Untitled Project'}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {currentProject?.description || 'Start a conversation to define your project'}
              </p>
              {currentProject?.scope && currentProject.scope.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Scope:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {currentProject.scope.slice(0, 3).map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {item}
                      </Badge>
                    ))}
                    {currentProject.scope.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{currentProject.scope.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Progress Overview */}
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-200 dark:border-blue-700">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Goals</span>
              </div>
              <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {completedGoals}/{totalGoals}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0}% Complete
              </div>
            </div>
            
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-200 dark:border-blue-700">
              <div className="flex items-center gap-2 mb-1">
                <Target className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Milestones</span>
              </div>
              <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {completedMilestones}/{totalMilestones}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0}% Complete
              </div>
            </div>
          </div>
          
          {/* Goals & Milestones */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Short-term Goals */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">Short-term Goals</Label>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => handleAddGoal('short-term')}
                  className="h-6 px-2 text-xs"
                >
                  <Plus className="w-3 h-3" />
                </Button>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {shortTermGoals.length === 0 ? (
                  <p className="text-xs text-gray-500 dark:text-gray-400 italic">No short-term goals yet</p>
                ) : (
                  shortTermGoals.map(goal => (
                    <div key={goal.id} className="flex items-start gap-2 text-sm">
                      <Checkbox 
                        checked={goal.completed} 
                        onCheckedChange={(checked) => handleToggleGoal(goal.id, checked as boolean)}
                        className="mt-0.5"
                      />
                      <span className={`flex-1 ${goal.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-gray-100'}`}>
                        {goal.text}
                      </span>
                      <Badge 
                        variant={goal.priority === 'high' ? 'destructive' : goal.priority === 'medium' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {goal.priority}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </div>
            
            {/* Long-term Goals */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">Long-term Objectives</Label>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => handleAddGoal('long-term')}
                  className="h-6 px-2 text-xs"
                >
                  <Plus className="w-3 h-3" />
                </Button>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {longTermGoals.length === 0 ? (
                  <p className="text-xs text-gray-500 dark:text-gray-400 italic">No long-term goals yet</p>
                ) : (
                  longTermGoals.map(goal => (
                    <div key={goal.id} className="flex items-start gap-2 text-sm">
                      <Checkbox 
                        checked={goal.completed} 
                        onCheckedChange={(checked) => handleToggleGoal(goal.id, checked as boolean)}
                        className="mt-0.5"
                      />
                      <span className={`flex-1 ${goal.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-gray-100'}`}>
                        {goal.text}
                      </span>
                      <Badge 
                        variant={goal.priority === 'high' ? 'destructive' : goal.priority === 'medium' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {goal.priority}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          {/* Milestones */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">Milestones</Label>
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={handleAddMilestone}
                className="h-6 px-2 text-xs"
              >
                <Plus className="w-3 h-3" />
              </Button>
            </div>
            <div className="space-y-2 max-h-24 overflow-y-auto">
              {projectMilestones.length === 0 ? (
                <p className="text-xs text-gray-500 dark:text-gray-400 italic">No milestones defined yet</p>
              ) : (
                projectMilestones.map(milestone => (
                  <div key={milestone.id} className="flex items-start gap-2 text-sm p-2 bg-white dark:bg-gray-800 rounded border">
                    <Checkbox 
                      checked={milestone.completed} 
                      onCheckedChange={(checked) => handleToggleMilestone(milestone.id, checked as boolean)}
                      className="mt-0.5"
                    />
                    <div className="flex-1">
                      <div className={`font-medium ${milestone.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-gray-100'}`}>
                        {milestone.title}
                      </div>
                      {milestone.description && (
                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {milestone.description}
                        </div>
                      )}
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {milestone.targetDate.toLocaleDateString()}
                        </span>
                        {milestone.completed && milestone.completedAt && (
                          <span className="text-xs text-green-600">
                            ✓ {milestone.completedAt.toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
          
          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2 pt-2 border-t border-blue-200 dark:border-blue-700">
            <Button size="sm" variant="outline" className="text-xs">
              <Edit className="w-3 h-3 mr-1" />
              Update Scope
            </Button>
            <Button size="sm" variant="outline" className="text-xs">
              <Target className="w-3 h-3 mr-1" />
              Add Constraint
            </Button>
            <Button size="sm" variant="outline" className="text-xs">
              <AlertCircle className="w-3 h-3 mr-1" />
              Define Success Criteria
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProjectNavigatorInterface;