// Agent configuration for workflow.md agents

export const AGENT_MODES = [
  {
    id: 'project-navigator',
    label: 'Navigator',
    icon: '🧭',
    description: 'Project definition & goal clarification',
    color: 'blue',
    placeholder: 'Explore ideas, clarify problems, define objectives...'
  },
  {
    id: 'prompt-engineering', 
    label: 'Prompt Eng',
    icon: '🧠',
    description: 'Prompt strategy & context curation',
    color: 'purple',
    placeholder: 'Design prompt strategies, curate context, optimize queries...'
  },
  {
    id: 'ai-execution',
    label: 'AI Exec', 
    icon: '⚙️',
    description: 'Advanced AI model execution',
    color: 'green',
    placeholder: 'Execute prompts, manage AI processing, control execution...'
  },
  {
    id: 'summarizer',
    label: 'Summarizer',
    icon: '✍️', 
    description: 'Insight extraction & distillation',
    color: 'orange',
    placeholder: 'Extract insights, identify patterns, summarize outputs...'
  },
  {
    id: 'general',
    label: 'General',
    icon: '💬',
    description: 'Casual conversations & general queries',
    color: 'gray',
    placeholder: 'Ask anything, casual conversation...'
  }
] as const;

export type AgentMode = typeof AGENT_MODES[number]['id'];

export const getAgentConfig = (mode: AgentMode) => {
  return AGENT_MODES.find(agent => agent.id === mode);
};

export const getAgentPlaceholder = (mode: AgentMode) => {
  return getAgentConfig(mode)?.placeholder || 'Type your message...';
};

export const getAgentIcon = (mode: AgentMode) => {
  return getAgentConfig(mode)?.icon || '💬';
};

export const getAgentLabel = (mode: AgentMode) => {
  return getAgentConfig(mode)?.label || 'Agent';
};

export const getAgentDescription = (mode: AgentMode) => {
  return getAgentConfig(mode)?.description || 'AI Agent';
};

export const getAgentColor = (mode: AgentMode) => {
  return getAgentConfig(mode)?.color || 'gray';
};