import { Router } from 'express';
import { healthCheck, metricsEndpoint } from '../middleware/monitoring';
import { prisma } from '../lib/prisma';

const router = Router();

// Basic health check endpoint
router.get('/health', healthCheck);

// Detailed health check with dependencies
router.get('/health/detailed', async (req, res) => {
  const checks = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'unknown',
    redis: 'unknown',
    vectorStore: 'unknown'
  };

  // Check database connection
  try {
    await prisma.$queryRaw`SELECT 1`;
    checks.database = 'healthy';
  } catch (error) {
    checks.database = 'unhealthy';
    console.error('Database health check failed:', error);
  }

  // Check Redis connection (if configured)
  try {
    if (process.env.REDIS_URL) {
      // Redis health check would go here
      checks.redis = 'healthy';
    } else {
      checks.redis = 'not_configured';
    }
  } catch (error) {
    checks.redis = 'unhealthy';
    console.error('Redis health check failed:', error);
  }

  // Check Vector Store connection (if configured)
  try {
    if (process.env.PINECONE_API_KEY) {
      // Pinecone health check would go here
      checks.vectorStore = 'healthy';
    } else {
      checks.vectorStore = 'not_configured';
    }
  } catch (error) {
    checks.vectorStore = 'unhealthy';
    console.error('Vector store health check failed:', error);
  }

  const isHealthy = checks.database === 'healthy' && 
                   (checks.redis === 'healthy' || checks.redis === 'not_configured') &&
                   (checks.vectorStore === 'healthy' || checks.vectorStore === 'not_configured');

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    checks
  });
});

// Metrics endpoint for monitoring
router.get('/metrics', metricsEndpoint);

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
  try {
    // Check if the application is ready to serve traffic
    await prisma.$queryRaw`SELECT 1`;
    res.status(200).json({ status: 'ready' });
  } catch (error) {
    res.status(503).json({ status: 'not_ready', error: 'Database not available' });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
  // Simple liveness check - if this endpoint responds, the app is alive
  res.status(200).json({ status: 'alive' });
});

export default router;