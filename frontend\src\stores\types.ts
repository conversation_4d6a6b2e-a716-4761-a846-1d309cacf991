
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  isPinned: boolean;
  timestamp: Date;
}

export interface ChecklistItem {
  id: string;
  task: string;
  isComplete: boolean;
}

export interface Conversation {
  id: string;
  name: string;
  createdAt: Date;
  parentId?: string; // For branched conversations
  isContextShielded: boolean; // Controls visibility to compass & plan agents
  messages: {
    core: Message[];
    plan: Message[];
  };
}

export interface ModelConfig {
  model: string;
  temperature: number;
  maxOutputTokens: number;
  thinkingBudget?: number;
}

export interface ExecutionConfig extends ModelConfig {
  contextAware: boolean;
  goalShielded: boolean;
  memoryRich: boolean;
  allowExternalModels: boolean;
}

export interface LoopConfig {
  automaticIteration: boolean;
  humanApprovalPoints: boolean;
  convergenceCriteria: string[];
  maxIterations: number;
}

// New workflow.md agent types
export interface Goal {
  id: string;
  text: string;
  type: 'short-term' | 'long-term';
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  completed: boolean;
  completedAt?: Date;
}

export interface ProjectSummary {
  id: string;
  title: string;
  description: string;
  scope: string[];
  assumptions: string[];
  constraints: string[];
  successCriteria: string[];
  lastUpdated: Date;
}

export interface PromptStrategy {
  id: string;
  name: string;
  queryStructure: string;
  contextDependencies: string[];
  targetModel: string;
  template: string;
  createdAt: Date;
}

export interface ContextBundle {
  id: string;
  name: string;
  content: string;
  type: 'summary' | 'output' | 'external' | 'user-input';
  weight: number;
  selected: boolean;
}

export interface ExecutionTask {
  id: string;
  promptId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  model: string;
  startTime?: Date;
  endTime?: Date;
  cost: number;
  progress: number;
}

export interface IterationCycle {
  id: string;
  cycleNumber: number;
  startTime: Date;
  endTime?: Date;
  insights: string[];
  pivotSuggestions: string[];
  gapsIdentified: string[];
  status: 'active' | 'completed' | 'paused';
}

export interface InsightExtraction {
  keyFindings: Array<{ id: string; text: string; confidence: number }>;
  contradictions: Array<{ id: string; text: string; severity: 'low' | 'medium' | 'high' }>;
  promisingLeads: Array<{ id: string; text: string; priority: number }>;
  themeTags: Array<{ id: string; name: string; count: number }>;
  variables: Array<{ id: string; name: string; description: string; impact: string; confidence: number }>;
  openQuestions: Array<{ id: string; text: string; category: string; priority: 'low' | 'medium' | 'high' }>;
}

export interface AppState {
  conversations: Conversation[];
  activeConversationId: string;
  generalChatMessages: Message[];
  
  // Replace old compass data with new summarizer data
  summarizerInsights: InsightExtraction;
  condensedSummary: string;
  
  // Updated agent loading states (workflow.md agents)
  isLoading: {
    projectNavigator: boolean;
    promptEngineering: boolean;
    advancedExecution: boolean;
    summarizer: boolean;
    iterativeLoop: boolean;
    general: boolean;
  };
  
  apiKeys: {
    openai: string;
    anthropic: string;
    google: string;
    openrouter: string;
  };
  
  // Updated model settings for workflow.md agents
  modelSettings: {
    projectNavigator: ModelConfig;
    promptEngineering: ModelConfig;
    advancedExecution: ExecutionConfig;
    summarizer: ModelConfig;
    iterativeLoop: LoopConfig;
    general: ModelConfig;
  };
  
  // New workflow.md specific state
  currentProject: ProjectSummary | null;
  projectGoals: Goal[];
  projectMilestones: Milestone[];
  promptStrategies: PromptStrategy[];
  contextBundles: ContextBundle[];
  executionQueue: ExecutionTask[];
  iterationHistory: IterationCycle[];
  
  // Goal shielding control
  goalShieldingEnabled: boolean;
  
  // Existing UI state
  isSettingsModalOpen: boolean;
  isBranchModalOpen: boolean;
  branchTitle: string;
  currentWorkspace: {
    name: string;
    description: string;
  } | null;
  draftPrompt: string;
}
