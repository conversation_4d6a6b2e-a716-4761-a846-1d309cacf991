# 🚀 Quick Start Guide - Docker Setup

You've configured your `.env` file with Docker database setup. Here's how to get SynergyAI running:

## ✅ Your Current Configuration

```bash
# Database (Docker - automatic setup)
DATABASE_URL="postgresql://thoughtsync:thoughtsync_password@localhost:5432/thoughtsync_db"

# Your Pinecone config
PINECONE_API_KEY="your-actual-api-key"
PINECONE_ENVIRONMENT="us-east-1"
PINECONE_INDEX_NAME="thoughtsync"

# Add these if you haven't already:
OPENAI_API_KEY="sk-your-openai-key"
GOOGLE_API_KEY="your-google-key"
JWT_SECRET="your-secure-jwt-secret-minimum-32-characters"
```

## 🚀 Start the Application

### 1. Start All Services
```bash
# Start PostgreSQL, Redis, Backend, and Frontend
docker-compose up -d
```

This will automatically:
- ✅ Create PostgreSQL database with your credentials
- ✅ Start Redis cache
- ✅ Build and start the backend API
- ✅ Build and start the frontend app

### 2. Run Database Migrations
```bash
# Set up database tables
docker-compose exec backend npm run db:migrate

# Generate Prisma client (if needed)
docker-compose exec backend npm run db:generate
```

### 3. Verify Everything is Working
```bash
# Check all services are running
docker-compose ps

# Check application health
curl http://localhost:3001/health/detailed

# Should show:
# - database: "healthy"
# - redis: "healthy" 
# - vectorStore: "healthy" (if Pinecone is configured)
```

## 🌐 Access Your Application

- **Frontend**: http://localhost:80
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health
- **Detailed Health**: http://localhost:3001/health/detailed

## 🔍 Troubleshooting

### If services fail to start:
```bash
# Check logs
docker-compose logs backend
docker-compose logs postgres
docker-compose logs frontend

# Restart specific service
docker-compose restart backend
```

### If database connection fails:
```bash
# Check if PostgreSQL is ready
docker-compose exec postgres pg_isready -U thoughtsync

# Check database exists
docker-compose exec postgres psql -U thoughtsync -d thoughtsync_db -c "SELECT version();"
```

### If you need to reset everything:
```bash
# Stop and remove all containers and volumes
docker-compose down -v

# Start fresh
docker-compose up -d
```

## 📝 Next Steps

1. **Test the AI features** - Make sure your OpenAI and Pinecone keys work
2. **Create a user account** - Use the frontend to register
3. **Start a project** - Test the AI workflow system
4. **Check monitoring** - Visit health endpoints to monitor performance

You're all set! 🎉