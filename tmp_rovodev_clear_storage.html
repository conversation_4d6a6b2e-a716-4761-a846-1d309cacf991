<!DOCTYPE html>
<html>
<head>
    <title>Clear SynergyAI Storage</title>
</head>
<body>
    <h1>Clear SynergyAI Browser Storage</h1>
    <p>This will clear all stored data including mock workspaces.</p>
    <button onclick="clearStorage()">Clear All Storage</button>
    <div id="result"></div>

    <script>
        function clearStorage() {
            // Clear localStorage
            localStorage.clear();
            
            // Clear sessionStorage
            sessionStorage.clear();
            
            // Clear IndexedDB (if used)
            if ('indexedDB' in window) {
                indexedDB.databases().then(databases => {
                    databases.forEach(db => {
                        if (db.name && (db.name.includes('synergyai') || db.name.includes('thoughtsync'))) {
                            indexedDB.deleteDatabase(db.name);
                        }
                    });
                });
            }
            
            document.getElementById('result').innerHTML = '<p style="color: green;">Storage cleared! Please refresh your SynergyAI application.</p>';
        }
    </script>
</body>
</html>