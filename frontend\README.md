
# AI Agent Chat

Welcome to AI Agent Chat, a sophisticated web application that allows you to interact with multiple specialized AI agents, each designed for specific purposes with advanced conversation management features.

## Features

### **Multi-Agent AI System**
- **Core AI**: Your systematic problem-solving assistant for structured analysis and detailed breakdowns
- **Plan AI**: A creative catalyst focused on ideation, strategy, and innovative approaches  
- **General AI**: A versatile assistant for casual conversations and general queries

### **Advanced Conversation Management**
- **Conversation Branching**: Create new conversation branches from Core AI while maintaining full context, perfect for exploring different solution paths
- **Context Shield**: Toggle visibility of conversations to Compass & Plan AI agents, giving you control over what information is shared
- **Real-time Chat**: Responsive and intuitive chat interface with message editing, pinning, and refresh capabilities
- **Conversation History**: Comprehensive message tracking with conversation organization and search

### **Smart Analytics & Guidance**
- **Compass Panel**: AI-powered analysis and progress tracking that adapts based on your visible conversations
- **Dynamic Checklists**: Automatically generated objectives that update based on your conversation progress
- **Strategic Insights**: Real-time summaries and recommendations from your AI interactions

### **Workspace Integration**
- **Custom Workspaces**: Create focused environments for specific projects or topics
- **Conversation Organization**: Manage multiple conversation threads with branching and categorization
- **Context Management**: Fine-grained control over information sharing between AI agents

## Key Features in Detail

### **Conversation Branching**
When working in Core AI mode, you can branch your conversation to explore alternative approaches:
1. Click the "Branch" button in Core AI mode (only available when messages exist)
2. Name your new branch conversation
3. The new branch starts with all existing Core messages copied over
4. Plan AI messages start fresh in the branch, allowing for new strategic directions

### **Context Shield**
Control what information your AI agents can see:
- **Unshielded** (default): Conversation is visible to Compass & Plan AI for comprehensive analysis
- **Shielded**: Conversation is hidden from Compass & Plan AI, keeping sensitive or experimental discussions private
- Toggle the shield status anytime using the Context Shield switch

### **Compass Analytics**
The Compass panel provides intelligent oversight:
- Analyzes only unshielded conversations to respect your privacy settings
- Generates dynamic progress tracking and strategic recommendations
- Updates automatically as conversations evolve
- Provides actionable insights and next-step suggestions

## LLM Integration Notes

### **Thinking Budget UI**
- The "Thinking Budget" user interface control in Settings only applies to Google Gemini models. The UI will not show this option for other providers.

### **OpenRouter and DeepSeek Reasoning**

#### OpenRouter Support
This app supports using [OpenRouter](https://openrouter.ai/) as an LLM provider, including models like DeepSeek that can return detailed reasoning in their responses.

#### Example: Using Reasoning with DeepSeek via OpenRouter

```typescript
import OpenAI from 'openai';

const openai = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: '<OPENROUTER_API_KEY>',
});

async function getResponseWithReasoning() {
  const response = await openai.chat.completions.create({
    model: 'deepseek/deepseek-r1',
    messages: [
      {
        role: 'user',
        content: "How would you build the world's tallest skyscraper?",
      },
    ],
    reasoning: {
      effort: 'high',
      exclude: true, // Use reasoning but don't include it directly in the response
    },
  });

  // TypeScript: explicit cast to support non-standard "reasoning" field
  const messageWithReasoning = response.choices[0]?.message as any;

  console.log('REASONING:', messageWithReasoning?.reasoning);
  console.log('CONTENT:', messageWithReasoning?.content);
}

getResponseWithReasoning();
```

- **Note**: The OpenRouter (DeepSeek and similar LLMs) may return a `.reasoning` property in their result message. The TypeScript types from the OpenAI SDK do not include this property, so you must use a type assertion (`as any`) to access `reasoning` in your code.

## Technology Stack

This project is built with modern web technologies:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript for better development experience
- **React** - Component-based UI framework
- **shadcn/ui** - Beautiful and accessible UI components
- **Tailwind CSS** - Utility-first CSS framework
- **Zustand** - Lightweight state management
- **React Router** - Client-side routing

## How to run locally

If you want to work locally using your own IDE, you can clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

## Usage Tips

### **Getting Started**
1. Start with a Core AI conversation to define your problem or goal
2. Use the branching feature to explore different solution approaches
3. Switch to Plan AI for creative ideation and strategic refinement
4. Monitor progress through the Compass panel
5. Use Context Shield for sensitive or experimental discussions

### **Best Practices**
- **Branch early and often** when exploring multiple solution paths
- **Use Context Shield** for brainstorming sessions you want to keep private
- **Pin important messages** for easy reference across conversations
- **Name your branches descriptively** to maintain clear conversation organization
- **Check the Compass panel regularly** for strategic insights and progress tracking

### **Workflow Recommendations**
1. **Problem Definition**: Start in Core AI to break down your challenge
2. **Solution Exploration**: Branch your conversation to explore different approaches
3. **Creative Enhancement**: Switch to Plan AI for innovative perspectives
4. **Progress Review**: Use Compass analytics to track advancement and identify next steps
5. **Context Management**: Apply Context Shield as needed for privacy control

This project was built with [Lovable](https://lovable.dev).

