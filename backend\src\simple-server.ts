import express from 'express';
import cors from 'cors';

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    message: 'SynergyAI Backend is running!' 
  });
});

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'SynergyAI API Server' });
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🚀 Simple SynergyAI Server running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

export default app;