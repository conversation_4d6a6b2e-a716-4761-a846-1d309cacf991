import { Router } from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { validateBody, validateParams } from '../middleware/validation';
import { createError } from '../middleware/errorHandler';
import { z } from 'zod';

const router = Router();

// All AI routes require authentication
router.use(authenticateToken);

// Schemas for AI operations
const executePromptSchema = z.object({
  conversationId: z.string().uuid('Invalid conversation ID'),
  agentType: z.enum(['project-navigator', 'prompt-engineering', 'ai-execution', 'summarizer', 'general']),
  prompt: z.string().min(1, 'Prompt is required'),
  model: z.string().min(1, 'Model is required'),
  contextBundles: z.array(z.string().uuid()).default([]),
  goalShielded: z.boolean().default(true),
  metadata: z.record(z.any()).default({}),
});

const taskParamsSchema = z.object({
  id: z.string().uuid('Invalid task ID'),
});

type ExecutePromptInput = z.infer<typeof executePromptSchema>;

// Helper function to check conversation access
const checkConversationAccess = async (conversationId: string, userId: string) => {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    include: {
      project: {
        include: {
          workspace: {
            include: {
              members: {
                where: { userId },
                select: { role: true }
              },
              owner: {
                select: { id: true }
              }
            }
          }
        }
      }
    }
  });

  if (!conversation) {
    throw createError('Conversation not found', 404);
  }

  const isOwner = conversation.project.workspace.owner.id === userId;
  const member = conversation.project.workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  return conversation;
};

// Execute AI prompt
router.post('/execute', validateBody(executePromptSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { 
      conversationId, 
      agentType, 
      prompt, 
      model, 
      contextBundles, 
      goalShielded, 
      metadata 
    }: ExecutePromptInput = req.body;

    // Verify conversation access
    await checkConversationAccess(conversationId, req.user!.id);

    // Create execution task
    const task = await prisma.executionTask.create({
      data: {
        conversationId,
        model,
        status: 'queued',
        progress: 0,
        cost: 0,
        createdBy: req.user!.id,
      }
    });

    // TODO: In a real implementation, this would:
    // 1. Queue the task for processing
    // 2. Apply context bundles and goal shielding
    // 3. Route to appropriate AI model
    // 4. Process the response
    // 5. Update the task status and result

    // For now, we'll simulate a quick response
    setTimeout(async () => {
      try {
        // TODO: Implement actual AI processing
        // For now, return a placeholder response
        const response = {
          content: `AI processing not yet implemented for ${agentType} agent.`,
          tokens: 0,
          cost: 0,
          model: model,
          timestamp: new Date().toISOString()
        };

        // Update task as completed
        await prisma.executionTask.update({
          where: { id: task.id },
          data: {
            status: 'completed',
            progress: 100,
            cost: response.cost,
            endTime: new Date(),
            result: response
          }
        });

        // Create assistant message with the response
        await prisma.message.create({
          data: {
            conversationId,
            agentType,
            role: 'assistant',
            content: response.content,
            metadata: {
              taskId: task.id,
              model: response.model,
              tokens: response.tokens,
              cost: response.cost,
              goalShielded,
              contextBundles
            },
            createdBy: req.user!.id,
          }
        });

        // Update conversation timestamp
        await prisma.conversation.update({
          where: { id: conversationId },
          data: { updatedAt: new Date() }
        });

      } catch (error) {
        console.error('Error processing AI task:', error);
        
        // Update task as failed
        await prisma.executionTask.update({
          where: { id: task.id },
          data: {
            status: 'failed',
            endTime: new Date(),
            errorMessage: 'Processing failed'
          }
        });
      }
    }, 2000); // 2 second delay to simulate processing

    res.status(201).json({
      message: 'AI execution task created successfully',
      task: {
        id: task.id,
        status: task.status,
        progress: task.progress,
        createdAt: task.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get execution task status
router.get('/tasks/:id', validateParams(taskParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const task = await prisma.executionTask.findUnique({
      where: { id },
      include: {
        conversation: {
          include: {
            project: {
              include: {
                workspace: {
                  include: {
                    members: {
                      where: { userId: req.user!.id },
                      select: { role: true }
                    },
                    owner: {
                      select: { id: true }
                    }
                  }
                }
              }
            }
          }
        },
        creator: {
          select: { id: true, username: true }
        }
      }
    });

    if (!task) {
      throw createError('Task not found', 404);
    }

    // Check access to the conversation
    const isOwner = task.conversation.project.workspace.owner.id === req.user!.id;
    const member = task.conversation.project.workspace.members[0];
    
    if (!isOwner && !member) {
      throw createError('Access denied', 403);
    }

    res.json({ task });
  } catch (error) {
    next(error);
  }
});

// Cancel execution task
router.post('/tasks/:id/cancel', validateParams(taskParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const task = await prisma.executionTask.findUnique({
      where: { id },
      include: {
        conversation: {
          include: {
            project: {
              include: {
                workspace: {
                  include: {
                    members: {
                      where: { userId: req.user!.id },
                      select: { role: true }
                    },
                    owner: {
                      select: { id: true }
                    }
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!task) {
      throw createError('Task not found', 404);
    }

    // Check access
    const isOwner = task.conversation.project.workspace.owner.id === req.user!.id;
    const member = task.conversation.project.workspace.members[0];
    
    if (!isOwner && !member) {
      throw createError('Access denied', 403);
    }

    // Can only cancel queued or processing tasks
    if (!['queued', 'processing'].includes(task.status)) {
      throw createError('Task cannot be cancelled', 400);
    }

    const updatedTask = await prisma.executionTask.update({
      where: { id },
      data: {
        status: 'failed',
        endTime: new Date(),
        errorMessage: 'Cancelled by user'
      }
    });

    res.json({
      message: 'Task cancelled successfully',
      task: updatedTask
    });
  } catch (error) {
    next(error);
  }
});

// Get conversation insights (for summarizer)
router.get('/conversations/:id/insights', validateParams(z.object({ id: z.string().uuid() })), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    await checkConversationAccess(id, req.user!.id);

    const insights = await prisma.insight.findMany({
      where: { conversationId: id },
      orderBy: { createdAt: 'desc' }
    });

    // Group insights by type
    const groupedInsights = {
      keyFindings: insights.filter(i => i.type === 'key-finding'),
      contradictions: insights.filter(i => i.type === 'contradiction'),
      promisingLeads: insights.filter(i => i.type === 'promising-lead'),
      themeTags: insights.filter(i => i.type === 'theme-tag'),
      variables: insights.filter(i => i.type === 'variable'),
      openQuestions: insights.filter(i => i.type === 'open-question'),
    };

    res.json({ insights: groupedInsights });
  } catch (error) {
    next(error);
  }
});

// Trigger conversation analysis (for summarizer)
router.post('/conversations/:id/analyze', validateParams(z.object({ id: z.string().uuid() })), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    await checkConversationAccess(id, req.user!.id);

    // TODO: In a real implementation, this would:
    // 1. Analyze all messages in the conversation
    // 2. Extract insights using AI
    // 3. Store insights in the database
    // 4. Update conversation summary

    // TODO: Implement actual conversation analysis
    res.status(501).json({
      error: 'Conversation analysis not yet implemented',
      message: 'This endpoint will be connected to actual AI analysis services in a future update'
    });
  } catch (error) {
    next(error);
  }
});

export { router as aiRoutes };