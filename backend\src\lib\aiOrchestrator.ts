import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { config } from '../config/config';
import { vectorStore, SearchResult } from './vectorStore';
import { prisma } from './prisma';
import { AGENT_SYSTEM_PROMPTS } from '../config/agentPrompts';

export interface AIModelConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'openrouter';
  model: string;
  temperature: number;
  maxTokens: number;
  contextAware: boolean;
  goalShielded: boolean;
}

export interface ContextBundle {
  id: string;
  name: string;
  content: string;
  type: 'summary' | 'output' | 'external' | 'user-input';
  weight: number;
  selected: boolean;
}

export interface ExecutionRequest {
  conversationId: string;
  agentType: 'project-navigator' | 'prompt-engineering' | 'ai-execution' | 'summarizer' | 'general';
  prompt: string;
  modelConfig: AIModelConfig;
  contextBundles: ContextBundle[];
  goalShielded: boolean;
  userId: string;
  metadata?: Record<string, any>;
}

export interface ExecutionResult {
  content: string;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  cost: number;
  model: string;
  provider: string;
  processingTime: number;
  contextUsed: string[];
  metadata: Record<string, any>;
}

class AIOrchestrator {
  private openai: OpenAI | null = null;
  private google: GoogleGenerativeAI | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeClients();
  }

  private initializeClients() {
    try {
      // Initialize OpenAI
      if (config.openaiApiKey) {
        this.openai = new OpenAI({
          apiKey: config.openaiApiKey,
        });
        console.log('✅ OpenAI client initialized');
      }

      // Initialize Google AI
      if (config.googleApiKey) {
        this.google = new GoogleGenerativeAI(config.googleApiKey);
        console.log('✅ Google AI client initialized');
      }

      // TODO: Initialize Anthropic and OpenRouter clients

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Error initializing AI clients:', error);
    }
  }

  async executePrompt(request: ExecutionRequest): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      // 1. Gather and prepare context
      const context = await this.prepareContext(request);

      // 2. Apply goal shielding if enabled
      const shieldedContext = request.goalShielded 
        ? await this.applyGoalShielding(context, request.conversationId)
        : context;

      // 3. Build the final prompt
      const finalPrompt = await this.buildPrompt(request, shieldedContext);

      // 4. Route to appropriate AI model
      const result = await this.routeToModel(request.modelConfig, finalPrompt, request.agentType);

      // 5. Process and return result
      const processingTime = Date.now() - startTime;

      const executionResult: ExecutionResult = {
        content: result.content,
        tokens: result.tokens,
        cost: result.cost,
        model: result.model,
        provider: request.modelConfig.provider,
        processingTime,
        contextUsed: shieldedContext.map(c => c.id),
        metadata: {
          agentType: request.agentType,
          goalShielded: request.goalShielded,
          contextBundlesUsed: request.contextBundles.length,
          ...request.metadata,
        },
      };

      // 6. Store context for future use
      await this.storeExecutionContext(request, executionResult);

      return executionResult;
    } catch (error) {
      console.error('Error executing AI prompt:', error);
      throw new Error(`AI execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async prepareContext(request: ExecutionRequest): Promise<ContextBundle[]> {
    const context: ContextBundle[] = [];

    // 1. Add explicitly selected context bundles
    context.push(...request.contextBundles.filter(c => c.selected));

    // 2. If context-aware, search for relevant context using vector store
    if (request.modelConfig.contextAware && vectorStore.isAvailable()) {
      try {
        const conversation = await prisma.conversation.findUnique({
          where: { id: request.conversationId },
          include: { project: true },
        });

        if (conversation) {
          const similarContext = await vectorStore.searchSimilarContext(
            request.prompt,
            conversation.project.id,
            {
              limit: 5,
              minScore: 0.7,
              agentType: request.agentType,
              excludeConversationId: request.conversationId,
            }
          );

          // Convert search results to context bundles
          const vectorContext: ContextBundle[] = similarContext.map((result, index) => ({
            id: `vector-${result.id}`,
            name: `Similar Context ${index + 1}`,
            content: result.content,
            type: 'summary',
            weight: result.score,
            selected: true,
          }));

          context.push(...vectorContext);
        }
      } catch (error) {
        console.error('Error fetching vector context:', error);
        // Continue without vector context
      }
    }

    // 3. Add conversation history context
    const conversationContext = await this.getConversationContext(request.conversationId, request.agentType);
    context.push(...conversationContext);

    // 4. Add project context
    const projectContext = await this.getProjectContext(request.conversationId);
    context.push(...projectContext);

    // 5. Sort by weight and limit total context size
    const sortedContext = context.sort((a, b) => b.weight - a.weight);
    return this.limitContextSize(sortedContext, request.modelConfig.maxTokens);
  }

  private async applyGoalShielding(
    context: ContextBundle[],
    conversationId: string
  ): Promise<ContextBundle[]> {
    try {
      // Get project ultimate goals
      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        include: {
          project: {
            include: {
              goals: {
                where: { type: 'long-term' },
                select: { text: true },
              },
            },
          },
        },
      });

      if (!conversation || conversation.project.goals.length === 0) {
        return context; // No goals to shield
      }

      const ultimateGoals = conversation.project.goals.map(g => g.text).join(', ');

      // Filter out context that might reveal ultimate goals
      // This is a simplified implementation - in production, you'd use NLP to detect goal-revealing content
      const shieldedContext = context.filter(ctx => {
        const content = ctx.content.toLowerCase();
        const goals = ultimateGoals.toLowerCase();
        
        // Simple keyword matching - could be enhanced with semantic analysis
        const goalKeywords = goals.split(/[,\s]+/).filter(word => word.length > 3);
        const hasGoalKeywords = goalKeywords.some(keyword => content.includes(keyword));
        
        return !hasGoalKeywords;
      });

      console.log(`🛡️ Goal shielding: filtered ${context.length - shieldedContext.length} context items`);
      return shieldedContext;
    } catch (error) {
      console.error('Error applying goal shielding:', error);
      return context; // Return original context if shielding fails
    }
  }

  private async buildPrompt(request: ExecutionRequest, context: ContextBundle[]): Promise<string> {
    const systemPrompt = AGENT_SYSTEM_PROMPTS[request.agentType] || AGENT_SYSTEM_PROMPTS.general;
    
    let prompt = `${systemPrompt}\n\n`;

    // Add context if available
    if (context.length > 0) {
      prompt += "CONTEXT:\n";
      context.forEach((ctx, index) => {
        prompt += `${index + 1}. ${ctx.name}: ${ctx.content}\n`;
      });
      prompt += "\n";
    }

    // Add the user's prompt
    prompt += `USER REQUEST:\n${request.prompt}\n\n`;

    // Add agent-specific instructions
    prompt += this.getAgentSpecificInstructions(request.agentType);

    return prompt;
  }

  private getAgentSpecificInstructions(agentType: string): string {
    const instructions = {
      'project-navigator': `
Please respond as the Project Navigator AI. Focus on:
- Breaking down the problem systematically
- Identifying goals, milestones, and success criteria
- Clarifying scope, assumptions, and constraints
- Providing actionable next steps
`,
      'prompt-engineering': `
Please respond as the Prompt Engineering AI. Focus on:
- Designing effective prompt strategies
- Optimizing query structure and context
- Recommending appropriate AI models
- Suggesting context dependencies and injection points
`,
      'ai-execution': `
Please respond as the AI Execution system. Focus on:
- Processing the request efficiently
- Maintaining context awareness
- Respecting goal shielding settings
- Providing structured, actionable outputs
`,
      'summarizer': `
Please respond as the Summarizer AI. Focus on:
- Extracting key insights and findings
- Identifying patterns, contradictions, and promising leads
- Generating actionable summaries
- Tagging themes and variables for future reference
`,
      'general': `
Please respond as a helpful AI assistant. Provide clear, accurate, and helpful information.
`,
    };

    return instructions[agentType] || instructions.general;
  }

  private async routeToModel(
    modelConfig: AIModelConfig,
    prompt: string,
    agentType: string
  ): Promise<{ content: string; tokens: any; cost: number; model: string }> {
    switch (modelConfig.provider) {
      case 'openai':
        return this.executeOpenAI(modelConfig, prompt);
      case 'google':
        return this.executeGoogle(modelConfig, prompt);
      case 'anthropic':
        return this.executeAnthropic(modelConfig, prompt);
      case 'openrouter':
        return this.executeOpenRouter(modelConfig, prompt);
      default:
        throw new Error(`Unsupported AI provider: ${modelConfig.provider}`);
    }
  }

  private async executeOpenAI(
    modelConfig: AIModelConfig,
    prompt: string
  ): Promise<{ content: string; tokens: any; cost: number; model: string }> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: modelConfig.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: modelConfig.temperature,
        max_tokens: modelConfig.maxTokens,
      });

      const content = response.choices[0]?.message?.content || '';
      const tokens = {
        prompt: response.usage?.prompt_tokens || 0,
        completion: response.usage?.completion_tokens || 0,
        total: response.usage?.total_tokens || 0,
      };

      // Calculate cost (simplified - actual costs vary by model)
      const cost = this.calculateOpenAICost(modelConfig.model, tokens);

      return {
        content,
        tokens,
        cost,
        model: modelConfig.model,
      };
    } catch (error) {
      console.error('OpenAI execution error:', error);
      throw new Error(`OpenAI execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async executeGoogle(
    modelConfig: AIModelConfig,
    prompt: string
  ): Promise<{ content: string; tokens: any; cost: number; model: string }> {
    if (!this.google) {
      throw new Error('Google AI client not initialized');
    }

    try {
      const model = this.google.getGenerativeModel({ model: modelConfig.model });
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const content = response.text();

      // Google AI doesn't provide detailed token usage in the same way
      const estimatedTokens = Math.ceil(content.length / 4); // Rough estimation

      return {
        content,
        tokens: {
          prompt: Math.ceil(prompt.length / 4),
          completion: estimatedTokens,
          total: Math.ceil((prompt.length + content.length) / 4),
        },
        cost: 0.001, // Simplified cost calculation
        model: modelConfig.model,
      };
    } catch (error) {
      console.error('Google AI execution error:', error);
      throw new Error(`Google AI execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async executeAnthropic(
    modelConfig: AIModelConfig,
    prompt: string
  ): Promise<{ content: string; tokens: any; cost: number; model: string }> {
    // TODO: Implement Anthropic Claude integration
    throw new Error('Anthropic integration not yet implemented');
  }

  private async executeOpenRouter(
    modelConfig: AIModelConfig,
    prompt: string
  ): Promise<{ content: string; tokens: any; cost: number; model: string }> {
    // TODO: Implement OpenRouter integration
    throw new Error('OpenRouter integration not yet implemented');
  }

  private calculateOpenAICost(model: string, tokens: any): number {
    // Simplified cost calculation - actual costs vary by model and change over time
    const costPer1KTokens = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.001, output: 0.002 },
    };

    const modelCosts = costPer1KTokens[model as keyof typeof costPer1KTokens] || costPer1KTokens['gpt-3.5-turbo'];
    
    const inputCost = (tokens.prompt / 1000) * modelCosts.input;
    const outputCost = (tokens.completion / 1000) * modelCosts.output;
    
    return inputCost + outputCost;
  }

  private async getConversationContext(
    conversationId: string,
    agentType: string
  ): Promise<ContextBundle[]> {
    try {
      const messages = await prisma.message.findMany({
        where: {
          conversationId,
          agentType,
        },
        orderBy: { createdAt: 'desc' },
        take: 10, // Last 10 messages
      });

      return messages.map((msg, index) => ({
        id: `msg-${msg.id}`,
        name: `Recent ${msg.role} message`,
        content: msg.content,
        type: 'summary' as const,
        weight: 0.8 - (index * 0.1), // Newer messages have higher weight
        selected: true,
      }));
    } catch (error) {
      console.error('Error fetching conversation context:', error);
      return [];
    }
  }

  private async getProjectContext(conversationId: string): Promise<ContextBundle[]> {
    try {
      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        include: {
          project: {
            include: {
              goals: { where: { completed: false } },
              milestones: { where: { completed: false } },
            },
          },
        },
      });

      if (!conversation) return [];

      const context: ContextBundle[] = [];

      // Add project summary
      context.push({
        id: `project-${conversation.project.id}`,
        name: 'Project Summary',
        content: `Project: ${conversation.project.title}\nDescription: ${conversation.project.description}\nScope: ${conversation.project.scope.join(', ')}`,
        type: 'summary',
        weight: 0.9,
        selected: true,
      });

      // Add active goals
      if (conversation.project.goals.length > 0) {
        const goalsText = conversation.project.goals.map(g => `- ${g.text} (${g.priority} priority)`).join('\n');
        context.push({
          id: `goals-${conversation.project.id}`,
          name: 'Active Goals',
          content: `Current project goals:\n${goalsText}`,
          type: 'summary',
          weight: 0.8,
          selected: true,
        });
      }

      // Add upcoming milestones
      if (conversation.project.milestones.length > 0) {
        const milestonesText = conversation.project.milestones.map(m => `- ${m.title}: ${m.targetDate?.toDateString()}`).join('\n');
        context.push({
          id: `milestones-${conversation.project.id}`,
          name: 'Upcoming Milestones',
          content: `Project milestones:\n${milestonesText}`,
          type: 'summary',
          weight: 0.7,
          selected: true,
        });
      }

      return context;
    } catch (error) {
      console.error('Error fetching project context:', error);
      return [];
    }
  }

  private limitContextSize(context: ContextBundle[], maxTokens: number): ContextBundle[] {
    const maxContextTokens = Math.floor(maxTokens * 0.6); // Reserve 60% for context, 40% for response
    let totalTokens = 0;
    const limitedContext: ContextBundle[] = [];

    for (const ctx of context) {
      const estimatedTokens = Math.ceil(ctx.content.length / 4); // Rough token estimation
      if (totalTokens + estimatedTokens <= maxContextTokens) {
        limitedContext.push(ctx);
        totalTokens += estimatedTokens;
      } else {
        break;
      }
    }

    return limitedContext;
  }

  private async storeExecutionContext(
    request: ExecutionRequest,
    result: ExecutionResult
  ): Promise<void> {
    try {
      // Store the execution result as context for future use
      if (vectorStore.isAvailable()) {
        await vectorStore.addContext({
          id: `execution-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content: `User: ${request.prompt}\n\nAssistant: ${result.content}`,
          metadata: {
            type: 'conversation',
            projectId: '', // Will be filled from conversation lookup
            conversationId: request.conversationId,
            userId: request.userId,
            timestamp: new Date(),
            agentType: request.agentType,
            tags: ['ai-execution', request.agentType],
          },
        });
      }
    } catch (error) {
      console.error('Error storing execution context:', error);
      // Don't throw - this is not critical for the main execution
    }
  }

  // Public utility methods
  async getAvailableModels(): Promise<Record<string, string[]>> {
    return {
      openai: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
      google: ['gemini-1.5-flash', 'gemini-1.5-pro'],
      anthropic: ['claude-3-haiku', 'claude-3-sonnet', 'claude-3-opus'],
      openrouter: ['meta-llama/llama-3-70b', 'anthropic/claude-3-haiku'],
    };
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unavailable';
    providers: Record<string, boolean>;
  }> {
    const providers = {
      openai: !!this.openai,
      google: !!this.google,
      anthropic: false, // TODO: Implement
      openrouter: false, // TODO: Implement
    };

    const availableProviders = Object.values(providers).filter(Boolean).length;
    
    let status: 'healthy' | 'degraded' | 'unavailable';
    if (availableProviders >= 2) {
      status = 'healthy';
    } else if (availableProviders === 1) {
      status = 'degraded';
    } else {
      status = 'unavailable';
    }

    return { status, providers };
  }
}

// Singleton instance
export const aiOrchestrator = new AIOrchestrator();
export default AIOrchestrator;